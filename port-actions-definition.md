
# Port.io Self-Service Actions Definition

This document defines a set of Port.io Self-Service Actions to automate common tasks, empower developers, and integrate AI capabilities into the Cortex methodology. These actions are the key to enabling the self-service and AI-driven aspects of your 5-point plan.

---

## Action 1: Scaffold New Cortex Component

**Identifier**: `scaffold-cortex-component`

**Title**: Scaffold New Cortex Component

**Description**: Creates a new component in a Git repository based on a template, ensuring it adheres to the Cortex methodology from the start. This action automates the initial setup and boilerplate, including the creation of the component's manifest file.

### User Inputs

| Input         | Title           | Type       | Description                                                                 |
|---------------|-----------------|------------|-----------------------------------------------------------------------------|
| `name`        | Component Name  | `string`   | The name of the new component (e.g., `user-authentication-service`).        |
| `description` | Description     | `string`   | A brief description of the component's purpose.                           |
| `system`      | Parent System   | `string`   | The parent system this component will belong to.                            |
| `repoUrl`     | Repository URL  | `string`   | The URL of the Git repository where the component should be created.        |

### Workflow

1.  **Trigger**: User fills out the form in the Port.io UI and clicks "Create".
2.  **API Call (SCM)**: Port.io makes an API call to your SCM provider (e.g., GitHub, GitLab).
3.  **Action (SCM)**: The SCM provider uses a pre-defined template repository to:
    a.  Create a new directory for the component.
    b.  Generate a `port.yml` file inside the new directory with the user-provided inputs.
    c.  Add boilerplate code, such as a `README.md`, `.gitignore`, and initial source files.
    d.  Commit the new files to the repository.
4.  **Feedback (Port.io)**: Port.io displays a link to the newly created component in the repository and in the software catalog.

---

## Action 2: Suggest Description (AI-Powered)

**Identifier**: `suggest-description-ai`

**Title**: Suggest Description (AI)

**Description**: Analyzes a component's source code and uses an AI model to suggest a description for it. This helps to enrich the software catalog and reduce the manual burden of documentation.

### User Inputs

| Input         | Title           | Type       | Description                                                                 |
|---------------|-----------------|------------|-----------------------------------------------------------------------------|
| `component`   | Component       | `entity`   | A reference to the component that needs a description.                      |

### Workflow

1.  **Trigger**: User clicks the "Suggest Description" button on a component's page in Port.io.
2.  **API Call (Port.io)**: Port.io retrieves the `codeBeacons.repoUrl` and relevant file paths for the selected component.
3.  **API Call (SCM)**: Port.io makes an API call to your SCM provider to fetch the content of the key source code files (e.g., the main entry point, primary service file).
4.  **API Call (AI Model)**: Port.io sends the source code content to a large language model (LLM) with a prompt like: `"Based on the following source code, please generate a concise, one-sentence description for this software component."`
5.  **Feedback (Port.io)**: Port.io displays the AI-generated description to the user, with a button to "Apply" it to the component's `description` field.

---

## Action 3: "What Would Break?" Impact Analysis

**Identifier**: `impact-analysis`

**Title**: "What Would Break?" Impact Analysis

**Description**: Provides a report of all the components that depend on a selected component. This is a powerful tool for understanding the potential blast radius of a change before it is made.

### User Inputs

| Input         | Title           | Type       | Description                                                                 |
|---------------|-----------------|------------|-----------------------------------------------------------------------------|
| `component`   | Component       | `entity`   | A reference to the component to be analyzed.                                |

### Workflow

1.  **Trigger**: User clicks the "Run Impact Analysis" button on a component's page in Port.io.
2.  **API Call (Port.io)**: Port.io queries its graph database to find all entities that have a `dependsOn` relation pointing to the selected component.
3.  **Feedback (Port.io)**: Port.io displays a list of the dependent components, with links to their pages in the catalog. The report could also be enriched with information about the owners of the dependent components to facilitate communication.

---

## Action 4: Request Ownership

**Identifier**: `request-ownership`

**Title**: Request Ownership

**Description**: Allows a user to request ownership of a component that is currently unowned. This helps to ensure that all components have a clear owner, improving accountability.

### User Inputs

| Input         | Title           | Type       | Description                                                                 |
|---------------|-----------------|------------|-----------------------------------------------------------------------------|
| `component`   | Component       | `entity`   | A reference to the unowned component.                                       |
| `requester`   | Requester       | `string`   | The name or email of the user requesting ownership.                         |

### Workflow

1.  **Trigger**: User clicks the "Request Ownership" button on an unowned component's page.
2.  **API Call (Ticketing System)**: Port.io makes an API call to your ticketing system (e.g., Jira, ServiceNow) to create a new ticket.
3.  **Action (Ticketing System)**: The new ticket is assigned to a default team (e.g., the architecture team) with a description like: `"User [requester] has requested ownership of the [component] component."`
4.  **Feedback (Port.io)**: Port.io displays a link to the newly created ticket and updates the component's status to `ownership-pending`.

---

## Action 5: Create Architecture Decision Record (ADR)

**Identifier**: `create-adr`

**Title**: Create ADR

**Description**: Creates a new Architecture Decision Record (ADR) in the repository, linked to a specific component. This facilitates the documentation of significant architectural decisions.

### User Inputs

| Input         | Title           | Type       | Description                                                                 |
|---------------|-----------------|------------|-----------------------------------------------------------------------------|
| `component`   | Component       | `entity`   | The component the ADR relates to.                                           |
| `adrTitle`    | ADR Title       | `string`   | The title of the new ADR.                                                   |
| `context`     | Context         | `string`   | The context or problem statement for the decision.                          |
| `decision`    | Decision        | `string`   | The decision that was made.                                                 |

### Workflow

1.  **Trigger**: User fills out the "Create ADR" form on a component's page.
2.  **API Call (SCM)**: Port.io makes an API call to your SCM provider to create a new Markdown file in the `docs/adrs` directory of the component's repository.
3.  **Action (SCM)**: The new file is created with a standard ADR template, populated with the user's inputs.
4.  **API Call (Port.io)**: Port.io updates the component's entity in the catalog, adding a link to the new ADR file under a `documentation` or `adr` relation.
5.  **Feedback (Port.io)**: Port.io displays a link to the newly created ADR file.
