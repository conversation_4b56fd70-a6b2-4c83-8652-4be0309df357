
# Port.io Blueprint Definition: Cortex Component

This document defines the schema for a "Cortex Component" Blueprint in Port.io. It is the direct implementation of the "Enhanced Soul Forge Template" and serves as the single source of truth for a component in the software catalog.

## Blueprint Identifier: `cortex-component`

## Blueprint Title: `Cortex Component`

## Icon: `Component`

## Schema Properties

The following properties define the data model for a Cortex Component.

---

### I. Foundation & Identity

These properties establish the basic identity and ownership of the component.

| Property      | Title       | Type     | Description                                                                    |
|---------------|-------------|----------|--------------------------------------------------------------------------------|
| `name`        | Name        | `string` | The unique, human-readable name of the component.                              |
| `description` | Description | `string` | A brief summary of the component's purpose and functionality.                   |
| `owner`       | Owner       | `string` | The team or individual responsible for the component's lifecycle.               |
| `system`      | System      | `string` | The parent system this component belongs to. This establishes the hierarchy.     |
| `lifecycle`   | Lifecycle   | `string` | The current lifecycle stage of the component. (Enum: `experimental`, `production`, `deprecated`) |

---

### II. Genesis & Context

This section, derived from the "Contextual Dimension," captures the business rationale behind the component.

| Property          | Title          | Type     | Description                                                  |
|-------------------|----------------|----------|--------------------------------------------------------------|
| `genesis.problem` | Problem        | `string` | The business problem this component is designed to solve.      |
| `genesis.solution`| Solution       | `string` | How this component addresses the problem.                      |
| `genesis.businessValue`| Business Value | `string` | The quantifiable business impact of this component.          |

---

### III. AI Index

This section provides structured data optimized for AI traversal and understanding, incorporating the "Spatial" and "Behavioral" dimensions.

| Property                      | Title                 | Type     | Description                                                              |
|-------------------------------|-----------------------|----------|--------------------------------------------------------------------------|
| `aiIndex.structural.purpose`  | Purpose               | `string` | A detailed explanation of the component's purpose and scope.            |
| `aiIndex.structural.pattern`  | Architectural Pattern | `string` | The primary architectural pattern used (e.g., Microservice, Library).    |
| `aiIndex.structural.boundaries`| Boundaries            | `string` | A description of the component's boundaries and responsibilities.       |
| `aiIndex.operational.performance`| Performance           | `string` | The expected performance characteristics and SLOs.                       |
| `aiIndex.operational.reliability`| Reliability           | `string` | The reliability requirements, including uptime and error budgets.        |
| `aiIndex.operational.security`| Security              | `string` | The security profile and any specific compliance requirements.           |

---

### IV. Functional Specification

This section details the specific functions within the component.

| Property                | Title             | Type      | Description                                                              |
|-------------------------|-------------------|-----------|--------------------------------------------------------------------------|
| `functions`             | Functions         | `array`   | A list of the component's primary functions.                              |
| `functions[].name`      | Function Name     | `string`  | The name of the function.                                                |
| `functions[].signature` | Signature         | `string`  | The function's signature, including parameters and return types.        |
| `functions[].description`| Description       | `string`  | A detailed description of what the function does.                        |
| `functions[].algorithmicFlow`| Algorithmic Flow  | `string`  | A step-by-step description of the function's logic.                     |
| `functions[].businessRules`| Business Rules    | `string`  | Any business rules or invariants the function enforces.                  |

---

### V. Data Models & Persistence

This section describes how the component handles data.

| Property                      | Title          | Type     | Description                                                              |
|-------------------------------|----------------|----------|--------------------------------------------------------------------------|
| `dataModels.schemas`          | Schemas        | `array`  | A list of data schemas owned by the component.                           |
| `dataModels.schemas[].name`   | Schema Name    | `string` | The name of the data schema.                                             |
| `dataModels.schemas[].description`| Description    | `string` | The purpose of the schema.                                               |
| `dataModels.schemas[].fields` | Fields         | `string` | The fields and data types within the schema.                             |
| `dataModels.persistence.strategy`| Persistence Strategy | `string` | The strategy used for data persistence (e.g., PostgreSQL, Redis).      |
| `dataModels.persistence.transactions`| Transaction Handling | `string` | How transactions and data consistency are managed.                       |

---

### VI. Asynchronous Specification

This section details the component's interaction with event-driven systems.

| Property                        | Title         | Type     | Description                                                              |
|---------------------------------|---------------|----------|--------------------------------------------------------------------------|
| `events.publishes`              | Publishes     | `array`  | A list of events the component publishes.                                |
| `events.publishes[].eventType`  | Event Type    | `string` | The type or name of the event.                                           |
| `events.publishes[].schema`     | Schema        | `string` | A description or link to the event's schema.                            |
| `events.subscribes`             | Subscribes    | `array`  | A list of events the component subscribes to.                            |
| `events.subscribes[].eventType` | Event Type    | `string` | The type or name of the subscribed event.                                |
| `events.subscribes[].handler`   | Handler       | `string` | A description of how the event is handled.                               |

---

### VII. Code Mapping (Code Beacons)

This section provides direct links to the component's source code.

| Property                | Title            | Type     | Description                                                              |
|-------------------------|------------------|----------|--------------------------------------------------------------------------|
| `codeBeacons.repoUrl`   | Repository URL   | `string` | The URL of the source code repository.                                   |
| `codeBeacons.entryPoints`| Entry Points     | `object` | Paths to the main entry points of the application.                       |
| `codeBeacons.services`  | Services         | `object` | Paths to the primary service implementations.                            |
| `codeBeacons.routes`    | Routes           | `object` | Paths to the API route definitions.                                      |
| `codeBeacons.models`    | Models           | `object` | Paths to the data model definitions.                                     |
| `codeBeacons.events`    | Event Handlers   | `object` | Paths to the asynchronous event handlers.                                |
| `codeBeacons.integrations`| Integrations     | `object` | Paths to code that integrates with external services.                    |
| `codeBeacons.configuration`| Configuration    | `object` | Paths to configuration files.                                            |
| `codeBeacons.tests`     | Tests            | `object` | Paths to the unit and integration tests.                                 |

## Relations

This section defines the relationships between this component and other entities in the catalog.

| Relation       | Title        | Target Blueprint   | Description                                                              |
|----------------|--------------|--------------------|--------------------------------------------------------------------------|
| `dependencies` | Dependencies | `cortex-component` | A list of other Cortex Components that this component depends on at runtime. |
| `api`          | APIs         | `api-spec`         | The OpenAPI specifications that this component provides or consumes.     |

