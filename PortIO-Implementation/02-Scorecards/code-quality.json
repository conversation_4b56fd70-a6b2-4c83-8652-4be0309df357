{"identifier": "code-quality", "title": "Code Quality", "description": "Ensures components maintain high code quality standards", "filter": {"combinator": "and", "conditions": [{"property": "$blueprint", "operator": "=", "value": "component"}]}, "levels": [{"color": "gold", "title": "Gold", "description": "Exceptional code quality with comprehensive testing and documentation", "rules": [{"identifier": "excellent-code-quality", "title": "Excellent Code Quality Score", "description": "Component has A-grade code quality", "level": "gold", "query": {"combinator": "and", "conditions": [{"property": "quality.codeQualityScore", "operator": "=", "value": "A"}]}}, {"identifier": "comprehensive-test-coverage", "title": "Comprehensive Test Coverage", "description": "Component has >90% test coverage", "level": "gold", "query": {"combinator": "and", "conditions": [{"property": "quality.testCoverage", "operator": ">=", "value": 90}]}}, {"identifier": "low-technical-debt", "title": "Low Technical Debt", "description": "Component has low technical debt", "level": "gold", "query": {"combinator": "and", "conditions": [{"property": "quality.technicalDebt", "operator": "=", "value": "low"}]}}, {"identifier": "recent-code-review", "title": "Recent Code Review", "description": "Component had code review within last 30 days", "level": "gold", "query": {"combinator": "and", "conditions": [{"property": "quality.lastCodeReview", "operator": ">=", "value": "{{ (now | date_modify(\"-30 days\")) | date(\"Y-m-d\") }}"}]}}, {"identifier": "ai-traversability-complete", "title": "AI Traversability Complete", "description": "Component has comprehensive AI traversability documentation", "level": "gold", "query": {"combinator": "and", "conditions": [{"property": "cortexMetadata.aiTraversability.functions", "operator": "isNotEmpty"}, {"property": "cortexMetadata.codeBeacons.entryPoint", "operator": "isNotEmpty"}, {"property": "cortexMetadata.codeBeacons.businessLogic", "operator": "isNotEmpty"}]}}]}, {"color": "silver", "title": "Silver", "description": "Good code quality with adequate testing and documentation", "rules": [{"identifier": "good-code-quality", "title": "Good Code Quality Score", "description": "Component has B-grade or better code quality", "level": "silver", "query": {"combinator": "and", "conditions": [{"property": "quality.codeQualityScore", "operator": "in", "value": ["A", "B"]}]}}, {"identifier": "good-test-coverage", "title": "Good Test Coverage", "description": "Component has >70% test coverage", "level": "silver", "query": {"combinator": "and", "conditions": [{"property": "quality.testCoverage", "operator": ">=", "value": 70}]}}, {"identifier": "moderate-technical-debt", "title": "Moderate Technical Debt", "description": "Component has low to medium technical debt", "level": "silver", "query": {"combinator": "and", "conditions": [{"property": "quality.technicalDebt", "operator": "in", "value": ["low", "medium"]}]}}, {"identifier": "has-code-beacons", "title": "Has Code Beacons", "description": "Component has basic code navigation beacons", "level": "silver", "query": {"combinator": "and", "conditions": [{"property": "cortexMetadata.codeBeacons.entryPoint", "operator": "isNotEmpty"}]}}, {"identifier": "has-language-framework", "title": "Has Language and Framework", "description": "Component has specified language and framework", "level": "silver", "query": {"combinator": "and", "conditions": [{"property": "language", "operator": "isNotEmpty"}, {"property": "framework", "operator": "isNotEmpty"}]}}]}, {"color": "bronze", "title": "Bronze", "description": "Minimum acceptable code quality standards", "rules": [{"identifier": "acceptable-code-quality", "title": "Acceptable Code Quality", "description": "Component has C-grade or better code quality", "level": "bronze", "query": {"combinator": "and", "conditions": [{"property": "quality.codeQualityScore", "operator": "in", "value": ["A", "B", "C"]}]}}, {"identifier": "basic-test-coverage", "title": "Basic Test Coverage", "description": "Component has >50% test coverage", "level": "bronze", "query": {"combinator": "and", "conditions": [{"property": "quality.testCoverage", "operator": ">=", "value": 50}]}}, {"identifier": "manageable-technical-debt", "title": "Manageable Technical Debt", "description": "Component does not have critical technical debt", "level": "bronze", "query": {"combinator": "and", "conditions": [{"property": "quality.technicalDebt", "operator": "!=", "value": "critical"}]}}, {"identifier": "has-repository-info", "title": "Has Repository Information", "description": "Component has repository URL and default branch", "level": "bronze", "query": {"combinator": "and", "conditions": [{"property": "repository.url", "operator": "isNotEmpty"}, {"property": "repository.defaultBranch", "operator": "isNotEmpty"}]}}, {"identifier": "has-basic-metadata", "title": "Has <PERSON> Metadata", "description": "Component has type, language, and description", "level": "bronze", "query": {"combinator": "and", "conditions": [{"property": "type", "operator": "isNotEmpty"}, {"property": "language", "operator": "isNotEmpty"}, {"property": "description", "operator": "isNotEmpty"}]}}]}]}