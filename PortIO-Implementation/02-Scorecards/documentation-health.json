{"identifier": "documentation-health", "title": "Documentation Health", "description": "Ensures components have comprehensive and up-to-date documentation", "filter": {"combinator": "and", "conditions": [{"property": "$blueprint", "operator": "in", "value": ["system", "feature", "component", "api"]}]}, "levels": [{"color": "gold", "title": "Gold", "description": "Exceptional documentation with comprehensive coverage and AI traversability", "rules": [{"identifier": "complete-documentation-suite", "title": "Complete Documentation Suite", "description": "Component has all essential documentation types", "level": "gold", "query": {"combinator": "and", "conditions": [{"property": "documentation.readme", "operator": "isNotEmpty"}, {"property": "documentation.architecture", "operator": "isNotEmpty"}, {"property": "documentation.runbook", "operator": "isNotEmpty"}]}}, {"identifier": "ai-traversability-complete", "title": "AI Traversability Complete", "description": "Component has comprehensive AI traversability documentation", "level": "gold", "query": {"combinator": "and", "conditions": [{"property": "cortexMetadata.aiTraversability", "operator": "isNotEmpty"}, {"property": "cortexMetadata.dimensionalContext", "operator": "isNotEmpty"}]}}, {"identifier": "business-context-documented", "title": "Business Context Documented", "description": "Component has clear business value and purpose documentation", "level": "gold", "query": {"combinator": "and", "conditions": [{"property": "businessValue", "operator": "isNotEmpty"}, {"property": "description", "operator": "isNotEmpty"}]}}, {"identifier": "api-documentation-complete", "title": "API Documentation Complete", "description": "APIs have comprehensive documentation including examples", "level": "gold", "query": {"combinator": "or", "conditions": [{"property": "$blueprint", "operator": "!=", "value": "api"}, {"combinator": "and", "conditions": [{"property": "specification.openapi", "operator": "isNotEmpty"}, {"property": "documentation.developerGuide", "operator": "isNotEmpty"}, {"property": "documentation.examples", "operator": "isNotEmpty"}]}]}}, {"identifier": "dimensional-documentation", "title": "Dimensional Documentation", "description": "Component has all four dimensional contexts documented", "level": "gold", "query": {"combinator": "and", "conditions": [{"property": "cortexMetadata.dimensionalContext.spatial", "operator": "isNotEmpty"}, {"property": "cortexMetadata.dimensionalContext.temporal", "operator": "isNotEmpty"}, {"property": "cortexMetadata.dimensionalContext.behavioral", "operator": "isNotEmpty"}, {"property": "cortexMetadata.dimensionalContext.contextual", "operator": "isNotEmpty"}]}}]}, {"color": "silver", "title": "Silver", "description": "Good documentation with essential coverage and some AI traversability", "rules": [{"identifier": "essential-documentation", "title": "Essential Documentation", "description": "Component has README and basic documentation", "level": "silver", "query": {"combinator": "and", "conditions": [{"property": "documentation.readme", "operator": "isNotEmpty"}, {"property": "description", "operator": "isNotEmpty"}]}}, {"identifier": "operational-documentation", "title": "Operational Documentation", "description": "Component has operational guidance", "level": "silver", "query": {"combinator": "or", "conditions": [{"property": "documentation.runbook", "operator": "isNotEmpty"}, {"property": "monitoring.dashboard", "operator": "isNotEmpty"}]}}, {"identifier": "basic-ai-traversability", "title": "Basic AI Traversability", "description": "Component has basic AI traversability information", "level": "silver", "query": {"combinator": "and", "conditions": [{"property": "cortexMetadata.aiTraversability", "operator": "isNotEmpty"}]}}, {"identifier": "api-specification", "title": "API Specification", "description": "APIs have specification documentation", "level": "silver", "query": {"combinator": "or", "conditions": [{"property": "$blueprint", "operator": "!=", "value": "api"}, {"property": "specification.openapi", "operator": "isNotEmpty"}]}}, {"identifier": "stakeholder-information", "title": "Stakeholder Information", "description": "Component has owner and stakeholder information", "level": "silver", "query": {"combinator": "and", "conditions": [{"property": "owner", "operator": "isNotEmpty"}]}}]}, {"color": "bronze", "title": "Bronze", "description": "Minimum documentation requirements", "rules": [{"identifier": "has-description", "title": "Has Description", "description": "Component has a meaningful description", "level": "bronze", "query": {"combinator": "and", "conditions": [{"property": "description", "operator": "isNotEmpty"}]}}, {"identifier": "has-owner", "title": "Has Owner", "description": "Component has an assigned owner", "level": "bronze", "query": {"combinator": "and", "conditions": [{"property": "owner", "operator": "isNotEmpty"}]}}, {"identifier": "basic-metadata", "title": "Basic Metadata", "description": "Component has basic identifying metadata", "level": "bronze", "query": {"combinator": "and", "conditions": [{"property": "lifecycle", "operator": "isNotEmpty"}]}}, {"identifier": "repository-link", "title": "Repository Link", "description": "Component has repository information (for components)", "level": "bronze", "query": {"combinator": "or", "conditions": [{"property": "$blueprint", "operator": "!=", "value": "component"}, {"property": "repository.url", "operator": "isNotEmpty"}]}}, {"identifier": "api-basic-info", "title": "API Basic Information", "description": "APIs have type, version, and base URL", "level": "bronze", "query": {"combinator": "or", "conditions": [{"property": "$blueprint", "operator": "!=", "value": "api"}, {"combinator": "and", "conditions": [{"property": "type", "operator": "isNotEmpty"}, {"property": "version", "operator": "isNotEmpty"}, {"property": "baseUrl", "operator": "isNotEmpty"}]}]}}]}]}