{"identifier": "security-compliance", "title": "Security Compliance", "description": "Ensures components meet security and compliance requirements", "filter": {"combinator": "and", "conditions": [{"property": "$blueprint", "operator": "in", "value": ["component", "api", "resource"]}]}, "levels": [{"color": "gold", "title": "Gold", "description": "Exceeds security standards with comprehensive protection and compliance", "rules": [{"identifier": "zero-vulnerabilities", "title": "Zero Open Vulnerabilities", "description": "Component has no open security vulnerabilities", "level": "gold", "query": {"combinator": "and", "conditions": [{"property": "security.vulnerabilities", "operator": "=", "value": 0}]}}, {"identifier": "recent-security-scan", "title": "Recent Security Scan", "description": "Component had security scan within last 7 days", "level": "gold", "query": {"combinator": "and", "conditions": [{"property": "security.lastSecurityScan", "operator": ">=", "value": "{{ (now | date_modify(\"-7 days\")) | date(\"Y-m-d\") }}"}]}}, {"identifier": "strong-authentication", "title": "Strong Authentication", "description": "Component uses strong authentication methods", "level": "gold", "query": {"combinator": "and", "conditions": [{"property": "security.authentication", "operator": "in", "value": ["o<PERSON>h", "jwt", "api-key"]}]}}, {"identifier": "comprehensive-compliance", "title": "Comprehensive Compliance", "description": "Component meets multiple compliance standards", "level": "gold", "query": {"combinator": "and", "conditions": [{"property": "compliance", "operator": "isNotEmpty"}]}}, {"identifier": "encryption-enabled", "title": "Encryption Enabled", "description": "Component has encryption at rest and in transit (for resources)", "level": "gold", "query": {"combinator": "or", "conditions": [{"property": "$blueprint", "operator": "!=", "value": "resource"}, {"combinator": "and", "conditions": [{"property": "security.encryption.atRest", "operator": "=", "value": true}, {"property": "security.encryption.inTransit", "operator": "=", "value": true}]}]}}]}, {"color": "silver", "title": "Silver", "description": "Meets security standards with good protection measures", "rules": [{"identifier": "low-vulnerabilities", "title": "Low Vulnerability Count", "description": "Component has ≤5 open vulnerabilities", "level": "silver", "query": {"combinator": "and", "conditions": [{"property": "security.vulnerabilities", "operator": "<=", "value": 5}]}}, {"identifier": "regular-security-scan", "title": "Regular Security Scan", "description": "Component had security scan within last 30 days", "level": "silver", "query": {"combinator": "and", "conditions": [{"property": "security.lastSecurityScan", "operator": ">=", "value": "{{ (now | date_modify(\"-30 days\")) | date(\"Y-m-d\") }}"}]}}, {"identifier": "basic-authentication", "title": "Basic Authentication", "description": "Component has authentication configured", "level": "silver", "query": {"combinator": "and", "conditions": [{"property": "security.authentication", "operator": "!=", "value": "none"}]}}, {"identifier": "some-compliance", "title": "Some Compliance Standards", "description": "Component meets at least one compliance standard", "level": "silver", "query": {"combinator": "and", "conditions": [{"property": "compliance", "operator": "isNotEmpty"}]}}, {"identifier": "basic-encryption", "title": "Basic Encryption", "description": "Component has at least encryption in transit (for resources)", "level": "silver", "query": {"combinator": "or", "conditions": [{"property": "$blueprint", "operator": "!=", "value": "resource"}, {"property": "security.encryption.inTransit", "operator": "=", "value": true}]}}]}, {"color": "bronze", "title": "Bronze", "description": "Minimum security requirements", "rules": [{"identifier": "manageable-vulnerabilities", "title": "Manageable Vulnerability Count", "description": "Component has ≤20 open vulnerabilities", "level": "bronze", "query": {"combinator": "and", "conditions": [{"property": "security.vulnerabilities", "operator": "<=", "value": 20}]}}, {"identifier": "has-security-scan", "title": "<PERSON> <PERSON>", "description": "Component has had at least one security scan", "level": "bronze", "query": {"combinator": "and", "conditions": [{"property": "security.lastSecurityScan", "operator": "isNotEmpty"}]}}, {"identifier": "authentication-specified", "title": "Authentication Specified", "description": "Component has authentication method specified", "level": "bronze", "query": {"combinator": "and", "conditions": [{"property": "security.authentication", "operator": "isNotEmpty"}]}}, {"identifier": "production-security-baseline", "title": "Production Security Baseline", "description": "Production components must have basic security measures", "level": "bronze", "query": {"combinator": "or", "conditions": [{"property": "lifecycle", "operator": "!=", "value": "production"}, {"combinator": "and", "conditions": [{"property": "security.vulnerabilities", "operator": "<=", "value": 10}, {"property": "security.authentication", "operator": "!=", "value": "none"}]}]}}, {"identifier": "has-owner-for-security", "title": "Has Owner for Security", "description": "Component has an owner responsible for security", "level": "bronze", "query": {"combinator": "and", "conditions": [{"property": "owner", "operator": "isNotEmpty"}]}}]}]}