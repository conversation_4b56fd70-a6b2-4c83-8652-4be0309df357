{"identifier": "operational-excellence", "title": "Operational Excellence", "description": "Ensures components meet operational standards for monitoring, alerting, and reliability", "filter": {"combinator": "and", "conditions": [{"property": "$blueprint", "operator": "in", "value": ["system", "component", "api", "resource"]}, {"property": "lifecycle", "operator": "in", "value": ["production", "development"]}]}, "levels": [{"color": "gold", "title": "Gold", "description": "Exceptional operational excellence with comprehensive monitoring and automation", "rules": [{"identifier": "comprehensive-monitoring", "title": "Comprehensive Monitoring", "description": "Component has complete monitoring setup", "level": "gold", "query": {"combinator": "and", "conditions": [{"property": "monitoring.dashboard", "operator": "isNotEmpty"}, {"property": "monitoring.alerts", "operator": "isNotEmpty"}, {"property": "monitoring.logs", "operator": "isNotEmpty"}]}}, {"identifier": "excellent-reliability", "title": "Excellent Reliability", "description": "Component meets strict reliability targets", "level": "gold", "query": {"combinator": "and", "conditions": [{"property": "performance.uptime", "operator": ">=", "value": 99.9}, {"property": "performance.errorRate", "operator": "<=", "value": 0.1}]}}, {"identifier": "sla-defined", "title": "SLA Defined", "description": "Component has well-defined SLA targets", "level": "gold", "query": {"combinator": "and", "conditions": [{"property": "sla.availability", "operator": "isNotEmpty"}, {"property": "sla.responseTime", "operator": "isNotEmpty"}]}}, {"identifier": "automated-health-checks", "title": "Automated Health Checks", "description": "Component has automated health monitoring", "level": "gold", "query": {"combinator": "or", "conditions": [{"property": "deployment.healthCheck", "operator": "isNotEmpty"}, {"property": "monitoring.healthCheck", "operator": "isNotEmpty"}]}}, {"identifier": "disaster-recovery", "title": "Disaster Recovery", "description": "Resources have disaster recovery plans", "level": "gold", "query": {"combinator": "or", "conditions": [{"property": "$blueprint", "operator": "!=", "value": "resource"}, {"property": "availability.disasterRecovery", "operator": "isNotEmpty"}]}}]}, {"color": "silver", "title": "Silver", "description": "Good operational standards with adequate monitoring and reliability", "rules": [{"identifier": "basic-monitoring", "title": "Basic Monitoring", "description": "Component has monitoring dashboard or alerts", "level": "silver", "query": {"combinator": "or", "conditions": [{"property": "monitoring.dashboard", "operator": "isNotEmpty"}, {"property": "monitoring.alerts", "operator": "isNotEmpty"}]}}, {"identifier": "good-reliability", "title": "Good Reliability", "description": "Component meets standard reliability targets", "level": "silver", "query": {"combinator": "and", "conditions": [{"property": "performance.uptime", "operator": ">=", "value": 99.5}, {"property": "performance.errorRate", "operator": "<=", "value": 1}]}}, {"identifier": "performance-tracking", "title": "Performance Tracking", "description": "Component tracks key performance metrics", "level": "silver", "query": {"combinator": "or", "conditions": [{"property": "performance.responseTime", "operator": "isNotEmpty"}, {"property": "performance.throughput", "operator": "isNotEmpty"}]}}, {"identifier": "backup-strategy", "title": "Backup Strategy", "description": "Resources have backup strategies", "level": "silver", "query": {"combinator": "or", "conditions": [{"property": "$blueprint", "operator": "!=", "value": "resource"}, {"property": "availability.backupStrategy", "operator": "isNotEmpty"}]}}, {"identifier": "operational-documentation", "title": "Operational Documentation", "description": "Component has operational runbook", "level": "silver", "query": {"combinator": "and", "conditions": [{"property": "documentation.runbook", "operator": "isNotEmpty"}]}}]}, {"color": "bronze", "title": "Bronze", "description": "Minimum operational requirements", "rules": [{"identifier": "has-owner-for-operations", "title": "Has Owner for Operations", "description": "Component has an owner responsible for operations", "level": "bronze", "query": {"combinator": "and", "conditions": [{"property": "owner", "operator": "isNotEmpty"}]}}, {"identifier": "basic-reliability", "title": "Basic Reliability", "description": "Component meets minimum reliability standards", "level": "bronze", "query": {"combinator": "and", "conditions": [{"property": "performance.uptime", "operator": ">=", "value": 99}, {"property": "performance.errorRate", "operator": "<=", "value": 5}]}}, {"identifier": "deployment-info", "title": "Deployment Information", "description": "Component has deployment configuration", "level": "bronze", "query": {"combinator": "or", "conditions": [{"property": "$blueprint", "operator": "=", "value": "system"}, {"property": "deployment.platform", "operator": "isNotEmpty"}]}}, {"identifier": "environment-specified", "title": "Environment Specified", "description": "Component has environment specified", "level": "bronze", "query": {"combinator": "or", "conditions": [{"property": "deployment.environment", "operator": "isNotEmpty"}, {"property": "environment", "operator": "isNotEmpty"}]}}, {"identifier": "production-readiness-check", "title": "Production Readiness Check", "description": "Production components must have basic operational setup", "level": "bronze", "query": {"combinator": "or", "conditions": [{"property": "lifecycle", "operator": "!=", "value": "production"}, {"combinator": "and", "conditions": [{"property": "owner", "operator": "isNotEmpty"}, {"property": "description", "operator": "isNotEmpty"}]}]}}]}]}