{"identifier": "production-readiness", "title": "Production Readiness", "description": "Ensures components meet production deployment standards", "filter": {"combinator": "and", "conditions": [{"property": "lifecycle", "operator": "in", "value": ["production", "development"]}, {"property": "$blueprint", "operator": "in", "value": ["component", "api"]}]}, "levels": [{"color": "gold", "title": "Gold", "description": "Exceeds production standards with comprehensive monitoring and automation", "rules": [{"identifier": "has-monitoring-dashboard", "title": "Has Monitoring Dashboard", "description": "Component has a dedicated monitoring dashboard", "level": "gold", "query": {"combinator": "and", "conditions": [{"property": "monitoring.dashboard", "operator": "isNotEmpty"}]}}, {"identifier": "has-automated-alerts", "title": "Has Automated Alerts", "description": "Component has automated alerting configured", "level": "gold", "query": {"combinator": "and", "conditions": [{"property": "monitoring.alerts", "operator": "isNotEmpty"}]}}, {"identifier": "high-test-coverage", "title": "High Test Coverage", "description": "Component has >80% test coverage", "level": "gold", "query": {"combinator": "and", "conditions": [{"property": "quality.testCoverage", "operator": ">=", "value": 80}]}}, {"identifier": "excellent-performance", "title": "Excellent Performance", "description": "Component meets strict performance targets", "level": "gold", "query": {"combinator": "and", "conditions": [{"property": "performance.responseTime", "operator": "<=", "value": 100}, {"property": "performance.errorRate", "operator": "<=", "value": 0.1}, {"property": "performance.uptime", "operator": ">=", "value": 99.9}]}}, {"identifier": "comprehensive-documentation", "title": "Comprehensive Documentation", "description": "Component has complete documentation suite", "level": "gold", "query": {"combinator": "and", "conditions": [{"property": "documentation.readme", "operator": "isNotEmpty"}, {"property": "documentation.runbook", "operator": "isNotEmpty"}, {"property": "documentation.architecture", "operator": "isNotEmpty"}]}}]}, {"color": "silver", "title": "Silver", "description": "Meets production standards with good monitoring and documentation", "rules": [{"identifier": "has-health-check", "title": "Has Health Check", "description": "Component has a health check endpoint", "level": "silver", "query": {"combinator": "and", "conditions": [{"property": "deployment.healthCheck", "operator": "isNotEmpty"}]}}, {"identifier": "adequate-test-coverage", "title": "Adequate Test Coverage", "description": "Component has >60% test coverage", "level": "silver", "query": {"combinator": "and", "conditions": [{"property": "quality.testCoverage", "operator": ">=", "value": 60}]}}, {"identifier": "good-performance", "title": "Good Performance", "description": "Component meets standard performance targets", "level": "silver", "query": {"combinator": "and", "conditions": [{"property": "performance.responseTime", "operator": "<=", "value": 500}, {"property": "performance.errorRate", "operator": "<=", "value": 1}, {"property": "performance.uptime", "operator": ">=", "value": 99.5}]}}, {"identifier": "basic-documentation", "title": "Basic Documentation", "description": "Component has essential documentation", "level": "silver", "query": {"combinator": "and", "conditions": [{"property": "documentation.readme", "operator": "isNotEmpty"}]}}, {"identifier": "deployment-configured", "title": "Deployment Configured", "description": "Component has deployment configuration", "level": "silver", "query": {"combinator": "and", "conditions": [{"property": "deployment.platform", "operator": "isNotEmpty"}, {"property": "deployment.environment", "operator": "isNotEmpty"}]}}]}, {"color": "bronze", "title": "Bronze", "description": "Minimum viable production requirements", "rules": [{"identifier": "has-owner", "title": "Has Owner", "description": "Component has an assigned owner team", "level": "bronze", "query": {"combinator": "and", "conditions": [{"property": "owner", "operator": "isNotEmpty"}]}}, {"identifier": "has-description", "title": "Has Description", "description": "Component has a meaningful description", "level": "bronze", "query": {"combinator": "and", "conditions": [{"property": "description", "operator": "isNotEmpty"}]}}, {"identifier": "has-repository", "title": "Has Repository", "description": "Component has a linked repository", "level": "bronze", "query": {"combinator": "and", "conditions": [{"property": "repository.url", "operator": "isNotEmpty"}]}}, {"identifier": "minimal-test-coverage", "title": "Minimal Test Coverage", "description": "Component has >30% test coverage", "level": "bronze", "query": {"combinator": "and", "conditions": [{"property": "quality.testCoverage", "operator": ">=", "value": 30}]}}, {"identifier": "production-lifecycle", "title": "Production Lifecycle", "description": "Component is marked as production ready", "level": "bronze", "query": {"combinator": "and", "conditions": [{"property": "lifecycle", "operator": "=", "value": "production"}]}}]}]}