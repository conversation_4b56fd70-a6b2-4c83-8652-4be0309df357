{"identifier": "api", "title": "API", "icon": "API", "description": "An Application Programming Interface that provides access to component functionality", "schema": {"properties": {"type": {"type": "string", "title": "API Type", "enum": ["REST", "GraphQL", "gRPC", "WebSocket", "Webhook"], "enumColors": {"REST": "blue", "GraphQL": "purple", "gRPC": "green", "WebSocket": "orange", "Webhook": "yellow"}}, "owner": {"type": "string", "format": "team", "title": "Owner Team", "description": "The team responsible for this API"}, "lifecycle": {"type": "string", "title": "Lifecycle Stage", "enum": ["experimental", "development", "production", "deprecated"], "enumColors": {"experimental": "lightGray", "development": "yellow", "production": "green", "deprecated": "red"}}, "version": {"type": "string", "title": "API Version", "description": "Current version of the API"}, "description": {"type": "string", "title": "Description", "description": "Detailed description of the API's purpose and functionality"}, "baseUrl": {"type": "string", "format": "url", "title": "Base URL", "description": "Base URL for the API"}, "specification": {"type": "object", "title": "API Specification", "properties": {"openapi": {"type": "string", "format": "url", "title": "OpenAPI Specification"}, "schema": {"type": "string", "format": "url", "title": "Schema Definition"}, "postman": {"type": "string", "format": "url", "title": "Postman Collection"}}}, "authentication": {"type": "object", "title": "Authentication", "properties": {"type": {"type": "string", "title": "Authentication Type", "enum": ["none", "basic", "bearer", "oauth2", "api-key", "jwt"]}, "description": {"type": "string", "title": "Authentication Description"}, "scopes": {"type": "array", "title": "<PERSON><PERSON><PERSON>", "items": {"type": "string"}}}}, "endpoints": {"type": "array", "title": "Key Endpoints", "description": "Important endpoints provided by this API", "items": {"type": "object", "properties": {"path": {"type": "string", "title": "Endpoint Path"}, "method": {"type": "string", "title": "HTTP Method", "enum": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"]}, "description": {"type": "string", "title": "Endpoint Description"}, "deprecated": {"type": "boolean", "title": "Deprecated"}}}}, "rateLimit": {"type": "object", "title": "Rate Limiting", "properties": {"requests": {"type": "number", "title": "Requests per Period"}, "period": {"type": "string", "title": "Time Period", "enum": ["second", "minute", "hour", "day"]}, "burst": {"type": "number", "title": "Burst Limit"}}}, "sla": {"type": "object", "title": "Service Level Agreement", "properties": {"availability": {"type": "string", "title": "Availability Target", "default": "99.9%"}, "responseTime": {"type": "string", "title": "Response Time Target", "default": "200ms"}, "throughput": {"type": "string", "title": "Throughput Target"}}}, "monitoring": {"type": "object", "title": "API Monitoring", "properties": {"dashboard": {"type": "string", "format": "url", "title": "API Dashboard"}, "healthCheck": {"type": "string", "format": "url", "title": "Health Check Endpoint"}, "metrics": {"type": "object", "title": "Current Metrics", "properties": {"requestsPerMinute": {"type": "number", "title": "Requests per Minute"}, "averageResponseTime": {"type": "number", "title": "Average Response Time (ms)"}, "errorRate": {"type": "number", "title": "Error Rate %", "minimum": 0, "maximum": 100}, "uptime": {"type": "number", "title": "Uptime %", "minimum": 0, "maximum": 100}}}}}, "consumers": {"type": "array", "title": "API Consumers", "description": "Known consumers of this API", "items": {"type": "object", "properties": {"name": {"type": "string", "title": "Consumer Name"}, "type": {"type": "string", "title": "Consumer Type", "enum": ["internal", "external", "partner", "public"]}, "usage": {"type": "string", "title": "Usage Pattern"}}}}, "documentation": {"type": "object", "title": "Documentation Links", "properties": {"developerGuide": {"type": "string", "format": "url", "title": "Developer Guide"}, "examples": {"type": "string", "format": "url", "title": "Code Examples"}, "changelog": {"type": "string", "format": "url", "title": "Changelog"}, "migration": {"type": "string", "format": "url", "title": "Migration Guide"}}}, "cortexMetadata": {"type": "object", "title": "Cortex Methodology Metadata", "description": "Preserved Cortex methodology information", "properties": {"aiTraversability": {"type": "object", "title": "AI Traversability Data", "properties": {"businessPurpose": {"type": "string", "title": "Business Purpose"}, "dataContracts": {"type": "array", "title": "Data Contracts", "items": {"type": "object", "properties": {"endpoint": {"type": "string", "title": "Endpoint"}, "inputContract": {"type": "string", "title": "Input Data Contract"}, "outputContract": {"type": "string", "title": "Output Data Contract"}, "businessRules": {"type": "string", "title": "Business Rules"}, "validationRules": {"type": "string", "title": "Validation Rules"}}}}, "useCases": {"type": "array", "title": "Primary Use Cases", "items": {"type": "object", "properties": {"scenario": {"type": "string", "title": "Use Case Scenario"}, "flow": {"type": "string", "title": "Request/Response Flow"}, "businessValue": {"type": "string", "title": "Business Value"}, "testScenarios": {"type": "array", "title": "Test Scenarios", "items": {"type": "string"}}}}}}}}}}, "required": ["type", "owner", "lifecycle", "version", "description", "baseUrl"]}, "relations": {"component": {"title": "Provided by Component", "target": "component", "many": false, "required": true}, "feature": {"title": "Supports Feature", "target": "feature", "many": false, "required": false}, "dependsOnApis": {"title": "Depends on APIs", "target": "api", "many": true, "required": false}}, "calculationProperties": {}, "mirrorProperties": {}}