{"identifier": "resource", "title": "Resource", "icon": "Resource", "description": "Infrastructure resources and external dependencies used by components", "schema": {"properties": {"type": {"type": "string", "title": "Resource Type", "enum": ["database", "cache", "queue", "storage", "cdn", "load-balancer", "external-api", "third-party-service"], "enumColors": {"database": "blue", "cache": "green", "queue": "orange", "storage": "purple", "cdn": "yellow", "load-balancer": "lightGray", "external-api": "red", "third-party-service": "<PERSON><PERSON><PERSON>"}}, "owner": {"type": "string", "format": "team", "title": "Owner Team", "description": "The team responsible for this resource"}, "lifecycle": {"type": "string", "title": "Lifecycle Stage", "enum": ["experimental", "development", "production", "deprecated"], "enumColors": {"experimental": "lightGray", "development": "yellow", "production": "green", "deprecated": "red"}}, "description": {"type": "string", "title": "Description", "description": "Detailed description of the resource and its purpose"}, "provider": {"type": "string", "title": "Provider", "description": "Cloud provider or service provider", "enum": ["aws", "gcp", "azure", "on-premise", "third-party"]}, "region": {"type": "string", "title": "Region/Location", "description": "Geographic region or data center location"}, "environment": {"type": "string", "title": "Environment", "enum": ["development", "staging", "production", "shared"], "enumColors": {"development": "yellow", "staging": "orange", "production": "green", "shared": "blue"}}, "configuration": {"type": "object", "title": "Configuration", "properties": {"size": {"type": "string", "title": "Size/Capacity"}, "version": {"type": "string", "title": "Version"}, "instanceType": {"type": "string", "title": "Instance Type"}, "storage": {"type": "string", "title": "Storage Configuration"}, "networking": {"type": "string", "title": "Network Configuration"}}}, "access": {"type": "object", "title": "Access Information", "properties": {"endpoint": {"type": "string", "title": "Connection Endpoint"}, "port": {"type": "number", "title": "Port"}, "protocol": {"type": "string", "title": "Protocol", "enum": ["HTTP", "HTTPS", "TCP", "UDP", "gRPC", "WebSocket"]}, "authentication": {"type": "string", "title": "Authentication Method", "enum": ["none", "username-password", "api-key", "certificate", "iam-role"]}}}, "performance": {"type": "object", "title": "Performance Metrics", "properties": {"throughput": {"type": "string", "title": "Throughput Capacity"}, "latency": {"type": "number", "title": "Average Latency (ms)"}, "iops": {"type": "number", "title": "IOPS (if applicable)"}, "bandwidth": {"type": "string", "title": "Bandwidth"}}}, "availability": {"type": "object", "title": "Availability & Reliability", "properties": {"sla": {"type": "string", "title": "SLA Target", "default": "99.9%"}, "uptime": {"type": "number", "title": "Current Uptime %", "minimum": 0, "maximum": 100}, "backupStrategy": {"type": "string", "title": "Backup Strategy"}, "disasterRecovery": {"type": "string", "title": "Disaster Recovery Plan"}}}, "cost": {"type": "object", "title": "Cost Information", "properties": {"monthlyEstimate": {"type": "number", "title": "Monthly Cost Estimate ($)"}, "pricingModel": {"type": "string", "title": "Pricing Model", "enum": ["fixed", "usage-based", "tiered", "spot", "reserved"]}, "costCenter": {"type": "string", "title": "Cost Center"}}}, "security": {"type": "object", "title": "Security Configuration", "properties": {"encryption": {"type": "object", "title": "Encryption", "properties": {"atRest": {"type": "boolean", "title": "Encryption at Rest"}, "inTransit": {"type": "boolean", "title": "Encryption in Transit"}, "keyManagement": {"type": "string", "title": "Key Management Service"}}}, "accessControl": {"type": "string", "title": "Access Control Method"}, "compliance": {"type": "array", "title": "Compliance Standards", "items": {"type": "string", "enum": ["SOC2", "GDPR", "HIPAA", "PCI-DSS", "ISO27001"]}}}}, "monitoring": {"type": "object", "title": "Monitoring Configuration", "properties": {"dashboard": {"type": "string", "format": "url", "title": "Monitoring Dashboard"}, "alerts": {"type": "array", "title": "Alert Configurations", "items": {"type": "object", "properties": {"metric": {"type": "string", "title": "Metric"}, "threshold": {"type": "string", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "severity": {"type": "string", "title": "Severity", "enum": ["low", "medium", "high", "critical"]}}}}, "healthCheck": {"type": "string", "format": "url", "title": "Health Check Endpoint"}}}, "documentation": {"type": "object", "title": "Documentation Links", "properties": {"setup": {"type": "string", "format": "url", "title": "Setup Guide"}, "configuration": {"type": "string", "format": "url", "title": "Configuration Documentation"}, "troubleshooting": {"type": "string", "format": "url", "title": "Troubleshooting Guide"}, "runbook": {"type": "string", "format": "url", "title": "Operational Runbook"}}}, "cortexMetadata": {"type": "object", "title": "Cortex Methodology Metadata", "description": "Preserved Cortex methodology information", "properties": {"operationalContext": {"type": "object", "title": "Operational Context", "properties": {"businessCriticality": {"type": "string", "title": "Business Criticality", "enum": ["low", "medium", "high", "critical"]}, "dataClassification": {"type": "string", "title": "Data Classification", "enum": ["public", "internal", "confidential", "restricted"]}, "usagePatterns": {"type": "string", "title": "Usage Patterns Description"}, "scalingTriggers": {"type": "array", "title": "<PERSON><PERSON> Triggers", "items": {"type": "string"}}, "maintenanceWindows": {"type": "array", "title": "Maintenance Windows", "items": {"type": "string"}}}}}}}, "required": ["type", "owner", "lifecycle", "description", "provider", "environment"]}, "relations": {"usedByComponents": {"title": "Used by Components", "target": "component", "many": true, "required": false}, "dependsOnResources": {"title": "Depends on Resources", "target": "resource", "many": true, "required": false}}, "calculationProperties": {}, "mirrorProperties": {}}