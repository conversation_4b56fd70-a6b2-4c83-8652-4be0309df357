{"identifier": "system", "title": "System", "icon": "Microservices", "description": "A major business capability or domain that contains multiple features and components", "schema": {"properties": {"owner": {"type": "string", "format": "team", "title": "Owner Team", "description": "The team responsible for this system"}, "domain": {"type": "string", "title": "Business Domain", "description": "The business domain this system belongs to", "enum": ["user-management", "payment", "inventory", "analytics", "platform"]}, "lifecycle": {"type": "string", "title": "Lifecycle Stage", "enum": ["experimental", "development", "production", "deprecated"], "enumColors": {"experimental": "lightGray", "development": "yellow", "production": "green", "deprecated": "red"}}, "description": {"type": "string", "title": "Description", "description": "Detailed description of the system's purpose and capabilities"}, "businessValue": {"type": "string", "title": "Business Value", "description": "The business value and impact this system provides"}, "stakeholders": {"type": "array", "title": "Stakeholders", "description": "Key stakeholders for this system", "items": {"type": "string", "format": "user"}}, "architecture": {"type": "object", "title": "Architecture Information", "properties": {"pattern": {"type": "string", "title": "Architecture Pattern", "enum": ["microservices", "monolith", "serverless", "hybrid"]}, "dataFlow": {"type": "string", "title": "Data Flow Description"}, "scalingStrategy": {"type": "string", "title": "Scaling Strategy"}}}, "compliance": {"type": "array", "title": "Compliance Requirements", "description": "Compliance standards this system must meet", "items": {"type": "string", "enum": ["SOC2", "GDPR", "HIPAA", "PCI-DSS", "ISO27001"]}}, "sla": {"type": "object", "title": "Service Level Agreement", "properties": {"availability": {"type": "string", "title": "Availability Target", "default": "99.9%"}, "responseTime": {"type": "string", "title": "Response Time Target", "default": "200ms"}, "throughput": {"type": "string", "title": "Throughput Target"}}}, "documentation": {"type": "object", "title": "Documentation Links", "properties": {"architecture": {"type": "string", "format": "url", "title": "Architecture Documentation"}, "runbook": {"type": "string", "format": "url", "title": "Operational Runbook"}, "apiDocs": {"type": "string", "format": "url", "title": "API Documentation"}}}, "monitoring": {"type": "object", "title": "Monitoring Configuration", "properties": {"dashboard": {"type": "string", "format": "url", "title": "Primary Dashboard"}, "alerting": {"type": "string", "format": "url", "title": "Alerting Configuration"}, "logs": {"type": "string", "format": "url", "title": "Log Aggregation"}}}, "cortexMetadata": {"type": "object", "title": "Cortex Methodology Metadata", "description": "Preserved Cortex methodology information", "properties": {"aiTraversability": {"type": "object", "title": "AI Traversability Data", "properties": {"businessPurpose": {"type": "string", "title": "Business Purpose"}, "keyFlows": {"type": "array", "title": "Key Business Flows", "items": {"type": "string"}}, "criticalFunctions": {"type": "array", "title": "Critical Functions", "items": {"type": "object", "properties": {"name": {"type": "string", "title": "Function Name"}, "purpose": {"type": "string", "title": "Business Purpose"}, "impact": {"type": "string", "title": "Business Impact"}}}}}}, "dimensionalContext": {"type": "object", "title": "Dimensional Documentation Context", "properties": {"spatial": {"type": "string", "title": "Spatial Context (Architecture & Data)"}, "temporal": {"type": "string", "title": "Temporal Context (History & Evolution)"}, "behavioral": {"type": "string", "title": "Behavioral Context (Function & Performance)"}, "contextual": {"type": "string", "title": "Contextual Context (Business & Technical Reality)"}}}}}}, "required": ["owner", "domain", "lifecycle", "description"]}, "relations": {"features": {"title": "Features", "target": "feature", "many": true, "required": false}, "components": {"title": "Components", "target": "component", "many": true, "required": false}}, "calculationProperties": {}, "mirrorProperties": {}}