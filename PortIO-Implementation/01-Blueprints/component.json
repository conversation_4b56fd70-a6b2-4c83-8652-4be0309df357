{"identifier": "component", "title": "Component", "icon": "Microservice", "description": "A deployable unit of software that implements specific functionality", "schema": {"properties": {"type": {"type": "string", "title": "Component Type", "enum": ["service", "library", "website", "database", "queue", "cache"], "enumColors": {"service": "blue", "library": "green", "website": "purple", "database": "orange", "queue": "yellow", "cache": "lightGray"}}, "owner": {"type": "string", "format": "team", "title": "Owner Team", "description": "The team responsible for this component"}, "lifecycle": {"type": "string", "title": "Lifecycle Stage", "enum": ["experimental", "development", "production", "deprecated"], "enumColors": {"experimental": "lightGray", "development": "yellow", "production": "green", "deprecated": "red"}}, "description": {"type": "string", "title": "Description", "description": "Detailed description of the component's purpose and functionality"}, "language": {"type": "string", "title": "Programming Language", "enum": ["javascript", "typescript", "python", "java", "go", "rust", "csharp", "php", "ruby"]}, "framework": {"type": "string", "title": "Framework/Technology", "description": "Primary framework or technology stack"}, "repository": {"type": "object", "title": "Repository Information", "properties": {"url": {"type": "string", "format": "url", "title": "Repository URL"}, "defaultBranch": {"type": "string", "title": "Default Branch", "default": "main"}, "visibility": {"type": "string", "title": "Visibility", "enum": ["public", "private", "internal"]}}}, "deployment": {"type": "object", "title": "Deployment Information", "properties": {"platform": {"type": "string", "title": "Deployment Platform", "enum": ["kubernetes", "docker", "serverless", "vm", "container"]}, "environment": {"type": "string", "title": "Current Environment", "enum": ["development", "staging", "production"]}, "url": {"type": "string", "format": "url", "title": "Deployment URL"}, "healthCheck": {"type": "string", "format": "url", "title": "Health Check Endpoint"}}}, "performance": {"type": "object", "title": "Performance Metrics", "properties": {"responseTime": {"type": "number", "title": "Average Response Time (ms)"}, "throughput": {"type": "number", "title": "Requests per Second"}, "errorRate": {"type": "number", "title": "Error Rate %", "minimum": 0, "maximum": 100}, "uptime": {"type": "number", "title": "Uptime %", "minimum": 0, "maximum": 100}}}, "security": {"type": "object", "title": "Security Information", "properties": {"vulnerabilities": {"type": "number", "title": "Open Vulnerabilities"}, "lastSecurityScan": {"type": "string", "format": "date-time", "title": "Last Security Scan"}, "authentication": {"type": "string", "title": "Authentication Method", "enum": ["none", "basic", "o<PERSON>h", "jwt", "api-key"]}}}, "quality": {"type": "object", "title": "Code Quality Metrics", "properties": {"testCoverage": {"type": "number", "title": "Test Coverage %", "minimum": 0, "maximum": 100}, "codeQualityScore": {"type": "string", "title": "Code Quality Score", "enum": ["A", "B", "C", "D", "F"]}, "technicalDebt": {"type": "string", "title": "Technical Debt Level", "enum": ["low", "medium", "high", "critical"]}, "lastCodeReview": {"type": "string", "format": "date-time", "title": "Last Code Review"}}}, "monitoring": {"type": "object", "title": "Monitoring Configuration", "properties": {"dashboard": {"type": "string", "format": "url", "title": "Monitoring Dashboard"}, "alerts": {"type": "array", "title": "Alert Configurations", "items": {"type": "object", "properties": {"name": {"type": "string", "title": "Alert <PERSON>"}, "condition": {"type": "string", "title": "<PERSON><PERSON>"}, "severity": {"type": "string", "title": "Severity", "enum": ["low", "medium", "high", "critical"]}}}}, "logs": {"type": "string", "format": "url", "title": "Log Aggregation URL"}}}, "documentation": {"type": "object", "title": "Documentation Links", "properties": {"readme": {"type": "string", "format": "url", "title": "README"}, "apiDocs": {"type": "string", "format": "url", "title": "API Documentation"}, "runbook": {"type": "string", "format": "url", "title": "Operational Runbook"}, "architecture": {"type": "string", "format": "url", "title": "Architecture Documentation"}}}, "cortexMetadata": {"type": "object", "title": "Cortex Methodology Metadata", "description": "Preserved Cortex methodology information", "properties": {"codeBeacons": {"type": "object", "title": "Code Navigation Beacons", "properties": {"entryPoint": {"type": "string", "title": "Main Entry Point"}, "businessLogic": {"type": "string", "title": "Business Logic Path"}, "dataModels": {"type": "string", "title": "Data Models Path"}, "configuration": {"type": "string", "title": "Configuration Path"}, "tests": {"type": "string", "title": "Tests Path"}}}, "aiTraversability": {"type": "object", "title": "AI Traversability Data", "properties": {"functions": {"type": "array", "title": "Function Documentation", "items": {"type": "object", "properties": {"name": {"type": "string", "title": "Function Name"}, "rationale": {"type": "string", "title": "Business Rationale"}, "parameters": {"type": "string", "title": "Parameters Description"}, "flow": {"type": "string", "title": "Logic Flow"}, "rules": {"type": "string", "title": "Business Rules"}, "testScenarios": {"type": "array", "title": "Test Scenarios", "items": {"type": "string"}}, "usageExamples": {"type": "array", "title": "Usage Examples", "items": {"type": "string"}}}}}}}}}}, "required": ["type", "owner", "lifecycle", "description", "language"]}, "relations": {"system": {"title": "Parent System", "target": "system", "many": false, "required": false}, "feature": {"title": "Parent Feature", "target": "feature", "many": false, "required": false}, "dependsOn": {"title": "Dependencies", "target": "component", "many": true, "required": false}, "apis": {"title": "Provides APIs", "target": "api", "many": true, "required": false}, "resources": {"title": "Uses Resources", "target": "resource", "many": true, "required": false}}, "calculationProperties": {}, "mirrorProperties": {}}