{"identifier": "feature", "title": "Feature", "icon": "Feature", "description": "A user-facing capability within a system that provides specific business value", "schema": {"properties": {"owner": {"type": "string", "format": "team", "title": "Owner Team", "description": "The team responsible for this feature"}, "lifecycle": {"type": "string", "title": "Lifecycle Stage", "enum": ["experimental", "development", "production", "deprecated"], "enumColors": {"experimental": "lightGray", "development": "yellow", "production": "green", "deprecated": "red"}}, "description": {"type": "string", "title": "Description", "description": "Detailed description of the feature's functionality"}, "userStories": {"type": "array", "title": "User Stories", "description": "Key user stories this feature addresses", "items": {"type": "string"}}, "businessValue": {"type": "string", "title": "Business Value", "description": "The business value this feature provides"}, "priority": {"type": "string", "title": "Priority", "enum": ["critical", "high", "medium", "low"], "enumColors": {"critical": "red", "high": "orange", "medium": "yellow", "low": "lightGray"}}, "featureFlags": {"type": "array", "title": "Feature Flags", "description": "Feature flags controlling this feature", "items": {"type": "object", "properties": {"name": {"type": "string", "title": "Flag Name"}, "enabled": {"type": "boolean", "title": "Enabled"}, "environment": {"type": "string", "title": "Environment"}}}}, "userInterface": {"type": "object", "title": "User Interface Information", "properties": {"mockups": {"type": "string", "format": "url", "title": "UI Mockups"}, "userFlow": {"type": "string", "format": "url", "title": "User Flow Diagram"}, "accessibility": {"type": "string", "title": "Accessibility Requirements"}}}, "analytics": {"type": "object", "title": "Analytics & Metrics", "properties": {"kpis": {"type": "array", "title": "Key Performance Indicators", "items": {"type": "string"}}, "trackingEvents": {"type": "array", "title": "Tracking Events", "items": {"type": "string"}}, "dashboard": {"type": "string", "format": "url", "title": "Analytics Dashboard"}}}, "testing": {"type": "object", "title": "Testing Information", "properties": {"testPlan": {"type": "string", "format": "url", "title": "Test Plan"}, "automatedTests": {"type": "boolean", "title": "Has Automated Tests"}, "testCoverage": {"type": "number", "title": "Test Coverage %", "minimum": 0, "maximum": 100}}}, "documentation": {"type": "object", "title": "Documentation Links", "properties": {"userGuide": {"type": "string", "format": "url", "title": "User Guide"}, "technicalSpec": {"type": "string", "format": "url", "title": "Technical Specification"}, "apiDocs": {"type": "string", "format": "url", "title": "API Documentation"}}}, "cortexMetadata": {"type": "object", "title": "Cortex Methodology Metadata", "description": "Preserved Cortex methodology information", "properties": {"aiTraversability": {"type": "object", "title": "AI Traversability Data", "properties": {"userJourney": {"type": "string", "title": "User Journey Description"}, "businessRules": {"type": "array", "title": "Business Rules", "items": {"type": "object", "properties": {"rule": {"type": "string", "title": "Rule Description"}, "rationale": {"type": "string", "title": "Business Rationale"}, "enforcement": {"type": "string", "title": "Enforcement Method"}}}}, "keyFunctions": {"type": "array", "title": "Key Functions", "items": {"type": "object", "properties": {"name": {"type": "string", "title": "Function Name"}, "purpose": {"type": "string", "title": "Business Purpose"}, "flow": {"type": "string", "title": "Logic Flow"}, "testScenarios": {"type": "array", "title": "Test Scenarios", "items": {"type": "string"}}}}}}}}}}, "required": ["owner", "lifecycle", "description", "businessValue"]}, "relations": {"system": {"title": "Parent System", "target": "system", "many": false, "required": true}, "components": {"title": "Components", "target": "component", "many": true, "required": false}, "apis": {"title": "APIs", "target": "api", "many": true, "required": false}}, "calculationProperties": {}, "mirrorProperties": {}}