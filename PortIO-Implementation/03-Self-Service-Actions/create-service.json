{"identifier": "create-service", "title": "Create New Service", "description": "This action creates a new microservice following the Cortex methodology:\n\n**What it does:**\n1. Creates a new GitHub repository with service template\n2. Sets up CI/CD pipeline configuration\n3. Creates the service entity in PortIO with complete metadata\n4. Configures monitoring and alerting templates\n5. Generates initial documentation with AI traversability\n6. Sets up code beacons for navigation\n\n**Cortex Methodology Integration:**\n- **Planning-First Development**: Requires business value and dimensional context before creation\n- **AI Traversability**: Captures function-level documentation for AI navigation\n- **Entity Hierarchy**: Properly links to parent system and feature\n- **Operational Excellence**: Sets up monitoring and deployment configuration\n\n**Post-Creation Steps:**\n1. Repository will be created with service template\n2. Service entity will appear in PortIO catalog\n3. Initial scorecards will be evaluated\n4. Team will receive notification with next steps\n\n**Prerequisites:**\n- Parent system must exist in PortIO\n- Owner team must be configured in PortIO\n- GitHub organization access required", "icon": "Microservice", "userInputs": {"properties": {"serviceName": {"title": "Service Name", "type": "string", "description": "Name of the new service (kebab-case)", "pattern": "^[a-z][a-z0-9-]*[a-z0-9]$"}, "description": {"title": "Service Description", "type": "string", "description": "Detailed description of the service's purpose and functionality"}, "businessValue": {"title": "Business Value", "type": "string", "description": "The business value this service provides"}, "ownerTeam": {"title": "Owner Team", "type": "string", "format": "team", "description": "Team responsible for this service"}, "parentSystem": {"title": "Parent System", "type": "string", "format": "entity", "blueprint": "system", "description": "System this service belongs to"}, "parentFeature": {"title": "Parent Feature (Optional)", "type": "string", "format": "entity", "blueprint": "feature", "description": "Feature this service supports (optional)"}, "language": {"title": "Programming Language", "type": "string", "enum": ["javascript", "typescript", "python", "java", "go", "rust", "csharp"], "description": "Primary programming language"}, "framework": {"title": "Framework", "type": "string", "description": "Framework or technology stack (e.g., Express.js, FastAPI, Spring Boot)"}, "repositoryName": {"title": "Repository Name", "type": "string", "description": "GitHub repository name (will be created)"}, "deploymentPlatform": {"title": "Deployment Platform", "type": "string", "enum": ["kubernetes", "docker", "serverless"], "description": "Target deployment platform"}, "aiTraversability": {"title": "AI Traversability Information", "type": "object", "description": "Core AI traversability metadata", "properties": {"businessPurpose": {"title": "Business Purpose", "type": "string", "description": "Clear business purpose for AI understanding"}, "keyFunctions": {"title": "Key Functions", "type": "array", "description": "List of key functions this service will provide", "items": {"type": "object", "properties": {"name": {"title": "Function Name", "type": "string"}, "purpose": {"title": "Business Purpose", "type": "string"}, "testScenarios": {"title": "Test Scenarios", "type": "array", "items": {"type": "string"}}}}}}}, "dimensionalContext": {"title": "Dimensional Documentation Context", "type": "object", "description": "Four-dimensional context for comprehensive understanding", "properties": {"spatial": {"title": "Spatial Context", "type": "string", "description": "Architecture and data relationships"}, "temporal": {"title": "Temporal Context", "type": "string", "description": "History and evolution context"}, "behavioral": {"title": "Behavioral Context", "type": "string", "description": "Function and performance characteristics"}, "contextual": {"title": "Contextual Context", "type": "string", "description": "Business and technical reality"}}}}, "required": ["serviceName", "description", "businessValue", "ownerTeam", "parentSystem", "language", "framework", "repositoryName", "deploymentPlatform"]}, "invocationMethod": {"type": "WEBHOOK", "url": "https://api.your-org.com/port-actions/create-service", "method": "POST", "headers": {"Authorization": "Bearer {{ .secrets.GITHUB_TOKEN }}"}, "body": {"serviceName": "{{ .inputs.serviceName }}", "description": "{{ .inputs.description }}", "businessValue": "{{ .inputs.businessValue }}", "ownerTeam": "{{ .inputs.ownerTeam }}", "parentSystem": "{{ .inputs.parentSystem }}", "parentFeature": "{{ .inputs.parentFeature }}", "language": "{{ .inputs.language }}", "framework": "{{ .inputs.framework }}", "repositoryName": "{{ .inputs.repositoryName }}", "deploymentPlatform": "{{ .inputs.deploymentPlatform }}", "aiTraversability": "{{ .inputs.aiTraversability }}", "dimensionalContext": "{{ .inputs.dimensionalContext }}", "runId": "{{ .run.id }}"}}, "trigger": {"type": "self-service", "operation": "CREATE", "userInputs": {"properties": {}, "required": []}}}