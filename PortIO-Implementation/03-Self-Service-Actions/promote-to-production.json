{"identifier": "promote-to-production", "title": "Promote to Production", "description": "This action promotes a component to production with comprehensive validation:\n\n**What it does:**\n1. Validates all Cortex methodology requirements\n2. Checks scorecard compliance (must be Silver or Gold)\n3. Validates testing evidence and operational readiness\n4. Verifies required approvals are in place\n5. Executes production deployment\n6. Updates component lifecycle to 'production'\n7. Sets up production monitoring and alerting\n8. Creates deployment record with rollback plan\n\n**Cortex Methodology Validation:**\n- **Planning-First Development**: Validates business justification and value\n- **AI Traversability**: Ensures AI documentation is complete\n- **Operational Excellence**: Validates monitoring, alerting, and runbooks\n- **Quality Gates**: Enforces test coverage and security requirements\n\n**Pre-Deployment Checks:**\n1. Component must have Silver or Gold scorecard rating\n2. All required approvals must be obtained\n3. Security scan must show acceptable vulnerability count\n4. Test coverage must meet minimum threshold\n5. Monitoring and alerting must be configured\n6. Documentation must be complete\n\n**Post-Deployment Actions:**\n1. Component lifecycle updated to 'production'\n2. Production monitoring activated\n3. SLA tracking begins\n4. Deployment record created\n5. Team notified of successful deployment\n6. Rollback plan activated for monitoring\n\n**Prerequisites:**\n- Component must exist in PortIO\n- Component must pass all Bronze scorecard requirements\n- Required approvers must be configured\n- Deployment pipeline must be configured", "icon": "Rocket", "userInputs": {"properties": {"component": {"title": "Component", "type": "string", "format": "entity", "blueprint": "component", "description": "Component to promote to production"}, "version": {"title": "Version", "type": "string", "description": "Version to promote (e.g., v1.2.3, commit SHA)"}, "releaseNotes": {"title": "Release Notes", "type": "string", "description": "Detailed release notes for this promotion"}, "businessJustification": {"title": "Business Justification", "type": "string", "description": "Business justification for this production promotion"}, "testingEvidence": {"title": "Testing Evidence", "type": "object", "description": "Evidence of comprehensive testing", "properties": {"unitTests": {"title": "Unit Test Results", "type": "object", "properties": {"coverage": {"title": "Test Coverage %", "type": "number", "minimum": 0, "maximum": 100}, "passed": {"title": "Tests Passed", "type": "number"}, "failed": {"title": "Tests Failed", "type": "number"}, "reportUrl": {"title": "Test Report URL", "type": "string", "format": "url"}}}, "integrationTests": {"title": "Integration Test Results", "type": "object", "properties": {"passed": {"title": "Tests Passed", "type": "number"}, "failed": {"title": "Tests Failed", "type": "number"}, "reportUrl": {"title": "Test Report URL", "type": "string", "format": "url"}}}, "performanceTests": {"title": "Performance Test Results", "type": "object", "properties": {"responseTime": {"title": "Average Response Time (ms)", "type": "number"}, "throughput": {"title": "Throughput (req/sec)", "type": "number"}, "errorRate": {"title": "Error Rate %", "type": "number"}, "reportUrl": {"title": "Performance Report URL", "type": "string", "format": "url"}}}, "securityScan": {"title": "Security Scan Results", "type": "object", "properties": {"vulnerabilities": {"title": "Open Vulnerabilities", "type": "number"}, "scanDate": {"title": "<PERSON>an Date", "type": "string", "format": "date"}, "reportUrl": {"title": "Security Report URL", "type": "string", "format": "url"}}}}}, "operationalReadiness": {"title": "Operational Readiness", "type": "object", "description": "Operational readiness checklist", "properties": {"monitoring": {"title": "Monitoring Setup", "type": "object", "properties": {"dashboardUrl": {"title": "Monitoring Dashboard URL", "type": "string", "format": "url"}, "alertsConfigured": {"title": "Alerts Configured", "type": "boolean"}, "healthCheckUrl": {"title": "Health Check URL", "type": "string", "format": "url"}}}, "documentation": {"title": "Documentation Completeness", "type": "object", "properties": {"runbookComplete": {"title": "Runbook Complete", "type": "boolean"}, "apiDocsComplete": {"title": "API Documentation Complete", "type": "boolean"}, "troubleshootingGuide": {"title": "Troubleshooting Guide Available", "type": "boolean"}}}, "backup": {"title": "Backup and Recovery", "type": "object", "properties": {"backupStrategy": {"title": "Backup Strategy Defined", "type": "boolean"}, "recoveryTested": {"title": "Recovery Process Tested", "type": "boolean"}, "rtoRpo": {"title": "RTO/RPO Defined", "type": "string", "description": "Recovery Time Objective / Recovery Point Objective"}}}}}, "approvals": {"title": "Required Approvals", "type": "object", "description": "Approvals required for production promotion", "properties": {"teamLead": {"title": "Team Lead Approval", "type": "string", "format": "user", "description": "Team lead who approves this promotion"}, "securityTeam": {"title": "Security Team Approval", "type": "boolean", "description": "Security team has approved this promotion"}, "architectureReview": {"title": "Architecture Review", "type": "boolean", "description": "Architecture review has been completed"}, "changeManagement": {"title": "Change Management Ticket", "type": "string", "description": "Change management ticket number"}}}, "rollbackPlan": {"title": "Rollback Plan", "type": "object", "description": "Plan for rolling back if issues occur", "properties": {"strategy": {"title": "Rollback Strategy", "type": "string", "enum": ["blue-green", "canary", "rolling", "immediate"], "description": "Deployment rollback strategy"}, "triggerCriteria": {"title": "Rollback <PERSON><PERSON>", "type": "string", "description": "Criteria that would trigger a rollback"}, "rollbackSteps": {"title": "Rollback Steps", "type": "array", "description": "Step-by-step rollback procedure", "items": {"type": "string"}}, "estimatedTime": {"title": "Estimated Rollback Time", "type": "string", "description": "Estimated time to complete rollback"}}}, "cortexValidation": {"title": "Cortex Methodology Validation", "type": "object", "description": "Cortex methodology compliance validation", "properties": {"aiTraversabilityComplete": {"title": "AI Traversability Complete", "type": "boolean", "description": "AI traversability documentation is complete"}, "dimensionalDocsComplete": {"title": "Dimensional Documentation Complete", "type": "boolean", "description": "Four-dimensional documentation is complete"}, "codeBeaconsUpdated": {"title": "Code Beacons Updated", "type": "boolean", "description": "Code navigation beacons are up to date"}, "businessValueValidated": {"title": "Business Value Validated", "type": "boolean", "description": "Business value has been validated by stakeholders"}}}}, "required": ["component", "version", "releaseNotes", "businessJustification", "testingEvidence", "operationalReadiness", "approvals", "rollbackPlan", "cortexValidation"]}, "invocationMethod": {"type": "WEBHOOK", "url": "https://api.your-org.com/port-actions/promote-to-production", "method": "POST", "headers": {"Authorization": "Bearer {{ .secrets.DEPLOYMENT_TOKEN }}"}, "body": {"component": "{{ .inputs.component }}", "version": "{{ .inputs.version }}", "releaseNotes": "{{ .inputs.releaseNotes }}", "businessJustification": "{{ .inputs.businessJustification }}", "testingEvidence": "{{ .inputs.testingEvidence }}", "operationalReadiness": "{{ .inputs.operationalReadiness }}", "approvals": "{{ .inputs.approvals }}", "rollbackPlan": "{{ .inputs.rollbackPlan }}", "cortexValidation": "{{ .inputs.cortexValidation }}", "runId": "{{ .run.id }}"}}, "trigger": {"type": "self-service", "operation": "DAY-2", "userInputs": {"properties": {}, "required": []}}}