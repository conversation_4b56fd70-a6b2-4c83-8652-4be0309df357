{"identifier": "onboard-team", "title": "Onboard New Team", "description": "This action onboards a new team with complete Cortex methodology setup:\n\n**What it does:**\n1. Creates team entity in PortIO with complete metadata\n2. Sets up team-specific scorecards and quality gates\n3. Configures tool access (GitHub, AWS, monitoring tools)\n4. Creates team documentation templates\n5. Sets up on-call rotation (if enabled)\n6. Configures Cortex methodology standards for the team\n7. Creates initial team dashboard in PortIO\n\n**Cortex Methodology Integration:**\n- **Planning-First Development**: Establishes team planning approach and standards\n- **AI Traversability**: Configures AI documentation requirements\n- **Quality Gates**: Sets up automated quality enforcement\n- **Operational Excellence**: Establishes monitoring and on-call procedures\n\n**Generated Artifacts:**\n1. Team entity in PortIO catalog\n2. Team-specific scorecard configurations\n3. GitHub team and repository access\n4. AWS IAM roles and policies\n5. Monitoring tool access\n6. Documentation templates\n7. On-call rotation setup (if enabled)\n\n**Post-Creation Steps:**\n1. Team will appear in PortIO catalog\n2. Team members will receive access notifications\n3. Scorecard evaluations will begin\n4. Team lead will receive onboarding checklist\n5. Documentation templates will be available\n\n**Prerequisites:**\n- Team lead must exist in user directory\n- Business domain must be configured in PortIO\n- Required tools (GitHub, AWS, etc.) must be configured", "icon": "Team", "userInputs": {"properties": {"teamName": {"title": "Team Name", "type": "string", "description": "Name of the new team (kebab-case)", "pattern": "^[a-z][a-z0-9-]*[a-z0-9]$"}, "teamDisplayName": {"title": "Team Display Name", "type": "string", "description": "Human-readable team name"}, "description": {"title": "Team Description", "type": "string", "description": "Description of the team's purpose and responsibilities"}, "businessDomain": {"title": "Business Domain", "type": "string", "enum": ["user-management", "payment", "inventory", "analytics", "platform", "infrastructure"], "description": "Primary business domain this team operates in"}, "teamLead": {"title": "Team Lead", "type": "string", "format": "user", "description": "Team lead/manager"}, "teamMembers": {"title": "Team Members", "type": "array", "description": "Initial team members", "items": {"type": "string", "format": "user"}}, "slackChannel": {"title": "Slack Channel", "type": "string", "description": "Team's Slack channel (e.g., #team-platform)"}, "emailGroup": {"title": "Email Group", "type": "string", "format": "email", "description": "Team's email group (e.g., <EMAIL>)"}, "responsibilities": {"title": "Team Responsibilities", "type": "array", "description": "Key areas of responsibility for this team", "items": {"type": "string"}}, "onCallRotation": {"title": "On-Call Rotation", "type": "object", "description": "On-call rotation configuration", "properties": {"enabled": {"title": "Enable On-Call", "type": "boolean", "default": false}, "schedule": {"title": "Rotation Schedule", "type": "string", "enum": ["weekly", "bi-weekly", "monthly"], "description": "How often the on-call rotation changes"}, "escalationPolicy": {"title": "Escalation Policy", "type": "string", "description": "Escalation policy for incidents"}}}, "toolAccess": {"title": "Tool Access Requirements", "type": "object", "description": "Required access to various tools and systems", "properties": {"github": {"title": "GitHub Access", "type": "object", "properties": {"organization": {"title": "GitHub Organization", "type": "string", "default": "your-org"}, "repositories": {"title": "Repository Access", "type": "array", "items": {"type": "string"}}, "permissions": {"title": "Permission Level", "type": "string", "enum": ["read", "write", "admin"], "default": "write"}}}, "aws": {"title": "AWS Access", "type": "object", "properties": {"accounts": {"title": "AWS Accounts", "type": "array", "items": {"type": "string"}}, "roles": {"title": "IAM Roles", "type": "array", "items": {"type": "string"}}}}, "monitoring": {"title": "Monitoring Tools", "type": "object", "properties": {"datadog": {"title": "Datadog Access", "type": "boolean", "default": true}, "pagerduty": {"title": "PagerDuty Access", "type": "boolean", "default": false}}}}}, "cortexMethodology": {"title": "Cortex Methodology Setup", "type": "object", "description": "Cortex methodology configuration for the team", "properties": {"planningApproach": {"title": "Planning Approach", "type": "string", "enum": ["agile", "waterfall", "hybrid"], "description": "Team's preferred planning methodology"}, "documentationStandards": {"title": "Documentation Standards", "type": "object", "properties": {"aiTraversability": {"title": "AI Traversability Required", "type": "boolean", "default": true, "description": "Require AI traversability documentation"}, "dimensionalDocs": {"title": "Dimensional Documentation Required", "type": "boolean", "default": true, "description": "Require four-dimensional documentation"}, "codeBeacons": {"title": "Code Beacons Required", "type": "boolean", "default": true, "description": "Require code navigation beacons"}}}, "qualityGates": {"title": "Quality Gates", "type": "object", "properties": {"minimumTestCoverage": {"title": "Minimum Test Coverage %", "type": "number", "default": 80, "minimum": 0, "maximum": 100}, "codeQualityThreshold": {"title": "Code Quality Threshold", "type": "string", "enum": ["A", "B", "C"], "default": "B"}, "securityScanFrequency": {"title": "Security <PERSON>an <PERSON>ncy", "type": "string", "enum": ["daily", "weekly", "monthly"], "default": "weekly"}}}}}}, "required": ["teamName", "teamDisplayName", "description", "businessDomain", "teamLead", "slackChannel", "emailGroup", "responsibilities"]}, "invocationMethod": {"type": "WEBHOOK", "url": "https://api.your-org.com/port-actions/onboard-team", "method": "POST", "headers": {"Authorization": "Bearer {{ .secrets.ADMIN_TOKEN }}"}, "body": {"teamName": "{{ .inputs.teamName }}", "teamDisplayName": "{{ .inputs.teamDisplayName }}", "description": "{{ .inputs.description }}", "businessDomain": "{{ .inputs.businessDomain }}", "teamLead": "{{ .inputs.teamLead }}", "teamMembers": "{{ .inputs.teamMembers }}", "slackChannel": "{{ .inputs.slackChannel }}", "emailGroup": "{{ .inputs.emailGroup }}", "responsibilities": "{{ .inputs.responsibilities }}", "onCallRotation": "{{ .inputs.onCallRotation }}", "toolAccess": "{{ .inputs.toolAccess }}", "cortexMethodology": "{{ .inputs.cortexMethodology }}", "runId": "{{ .run.id }}"}}, "trigger": {"type": "self-service", "operation": "CREATE", "userInputs": {"properties": {}, "required": []}}}