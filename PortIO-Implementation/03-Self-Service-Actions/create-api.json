{"identifier": "create-api", "title": "Create New API", "description": "This action creates a new API following the Cortex methodology:\n\n**What it does:**\n1. Creates the API entity in PortIO with complete metadata\n2. Generates OpenAPI specification template\n3. Sets up API documentation structure\n4. Configures monitoring and alerting for the API\n5. Creates initial data contracts and use case documentation\n6. Sets up rate limiting and SLA monitoring\n\n**Cortex Methodology Integration:**\n- **Planning-First Development**: Requires business value and use cases before creation\n- **AI Traversability**: Captures data contracts and business rules for AI understanding\n- **Entity Hierarchy**: Properly links to parent component and feature\n- **Operational Excellence**: Sets up SLA targets and monitoring\n\n**Generated Artifacts:**\n1. API entity in PortIO catalog\n2. OpenAPI specification template\n3. Developer documentation template\n4. Monitoring dashboard configuration\n5. Rate limiting rules\n6. Initial test scenarios\n\n**Post-Creation Steps:**\n1. API entity will appear in PortIO catalog\n2. OpenAPI spec will be generated in the component repository\n3. Documentation templates will be created\n4. Monitoring alerts will be configured\n5. Team will receive notification with next steps\n\n**Prerequisites:**\n- Parent component must exist in PortIO\n- Owner team must be configured in PortIO\n- Component must have repository configured", "icon": "API", "userInputs": {"properties": {"apiName": {"title": "API Name", "type": "string", "description": "Name of the new API (kebab-case)", "pattern": "^[a-z][a-z0-9-]*[a-z0-9]$"}, "apiType": {"title": "API Type", "type": "string", "enum": ["REST", "GraphQL", "gRPC", "WebSocket"], "description": "Type of API to create"}, "version": {"title": "Initial Version", "type": "string", "default": "v1", "description": "Initial API version"}, "description": {"title": "API Description", "type": "string", "description": "Detailed description of the API's purpose and functionality"}, "businessValue": {"title": "Business Value", "type": "string", "description": "The business value this API provides"}, "ownerTeam": {"title": "Owner Team", "type": "string", "format": "team", "description": "Team responsible for this API"}, "parentComponent": {"title": "Parent Component", "type": "string", "format": "entity", "blueprint": "component", "description": "Component that provides this API"}, "parentFeature": {"title": "Parent Feature (Optional)", "type": "string", "format": "entity", "blueprint": "feature", "description": "Feature this API supports (optional)"}, "baseUrl": {"title": "Base URL", "type": "string", "format": "url", "description": "Base URL for the API (e.g., https://api.example.com/v1)"}, "authentication": {"title": "Authentication", "type": "object", "description": "Authentication configuration", "properties": {"type": {"title": "Authentication Type", "type": "string", "enum": ["none", "basic", "bearer", "oauth2", "api-key", "jwt"]}, "description": {"title": "Authentication Description", "type": "string"}}}, "keyEndpoints": {"title": "Key Endpoints", "type": "array", "description": "Initial set of key endpoints", "items": {"type": "object", "properties": {"path": {"title": "Endpoint Path", "type": "string"}, "method": {"title": "HTTP Method", "type": "string", "enum": ["GET", "POST", "PUT", "DELETE", "PATCH"]}, "description": {"title": "Endpoint Description", "type": "string"}}}}, "rateLimit": {"title": "Rate Limiting", "type": "object", "description": "Rate limiting configuration", "properties": {"requests": {"title": "Requests per Period", "type": "number"}, "period": {"title": "Time Period", "type": "string", "enum": ["second", "minute", "hour", "day"]}}}, "sla": {"title": "Service Level Agreement", "type": "object", "description": "SLA targets for this API", "properties": {"availability": {"title": "Availability Target", "type": "string", "default": "99.9%"}, "responseTime": {"title": "Response Time Target", "type": "string", "default": "200ms"}}}, "aiTraversability": {"title": "AI Traversability Information", "type": "object", "description": "AI traversability metadata for the API", "properties": {"businessPurpose": {"title": "Business Purpose", "type": "string", "description": "Clear business purpose for AI understanding"}, "dataContracts": {"title": "Data Contracts", "type": "array", "description": "Input/output contracts for key endpoints", "items": {"type": "object", "properties": {"endpoint": {"title": "Endpoint", "type": "string"}, "inputContract": {"title": "Input Data Contract", "type": "string"}, "outputContract": {"title": "Output Data Contract", "type": "string"}, "businessRules": {"title": "Business Rules", "type": "string"}}}}, "useCases": {"title": "Primary Use Cases", "type": "array", "description": "Key use cases for this API", "items": {"type": "object", "properties": {"scenario": {"title": "Use Case Scenario", "type": "string"}, "flow": {"title": "Request/Response Flow", "type": "string"}, "businessValue": {"title": "Business Value", "type": "string"}}}}}}}, "required": ["apiName", "apiType", "version", "description", "businessValue", "ownerTeam", "parentComponent", "baseUrl", "authentication"]}, "invocationMethod": {"type": "WEBHOOK", "url": "https://api.your-org.com/port-actions/create-api", "method": "POST", "headers": {"Authorization": "Bearer {{ .secrets.GITHUB_TOKEN }}"}, "body": {"apiName": "{{ .inputs.apiName }}", "apiType": "{{ .inputs.apiType }}", "version": "{{ .inputs.version }}", "description": "{{ .inputs.description }}", "businessValue": "{{ .inputs.businessValue }}", "ownerTeam": "{{ .inputs.ownerTeam }}", "parentComponent": "{{ .inputs.parentComponent }}", "parentFeature": "{{ .inputs.parentFeature }}", "baseUrl": "{{ .inputs.baseUrl }}", "authentication": "{{ .inputs.authentication }}", "keyEndpoints": "{{ .inputs.keyEndpoints }}", "rateLimit": "{{ .inputs.rateLimit }}", "sla": "{{ .inputs.sla }}", "aiTraversability": "{{ .inputs.aiTraversability }}", "runId": "{{ .run.id }}"}}, "trigger": {"type": "self-service", "operation": "CREATE", "userInputs": {"properties": {}, "required": []}}}