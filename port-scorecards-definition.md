
# Port.io Scorecards Definition

This document defines a set of Port.io Scorecards to enforce the architectural standards of the Cortex methodology. These scorecards replace the custom validation scripts and provide a real-time, automated way to measure the quality and compliance of every component in the software catalog.

---

## Scorecard 1: Foundation Quality

**Identifier**: `foundation-quality`

**Title**: Foundation Quality

**Description**: Ensures that every component meets the absolute minimum standards for discoverability, ownership, and context.

### Rules

| Level | Title                  | Statement                                       | Rationale                                                                 |
|-------|------------------------|-------------------------------------------------|---------------------------------------------------------------------------|
| Gold  | Has Owner              | `owner` is not `null`                           | Every component must have a clear owner to ensure accountability.         |
| Gold  | Has Description        | `description` is not `null`                     | A component without a description is a black box.                         |
| Gold  | Has System             | `system` is not `null`                          | Components must belong to a system to establish architectural hierarchy.  |
| Gold  | Has Lifecycle          | `lifecycle` is not `null`                       | The lifecycle stage is critical for understanding a component's status.   |
| Silver| Has Business Value     | `genesis.businessValue` is not `null`           | Connecting components to business value is a core tenet of the Cortex methodology. |

---

## Scorecard 2: Documentation Completeness

**Identifier**: `documentation-completeness`

**Title**: Documentation Completeness

**Description**: Measures the richness and completeness of a component's documentation, focusing on the details required for both human and AI understanding.

### Rules

| Level  | Title                     | Statement                                         | Rationale                                                                    |
|--------|---------------------------|---------------------------------------------------|------------------------------------------------------------------------------|
| Gold   | Has Purpose               | `aiIndex.structural.purpose` is not `null`        | The "why" behind a component is the most critical piece of information.      |
| Gold   | Has Architectural Pattern | `aiIndex.structural.pattern` is not `null`      | Understanding the pattern is key to understanding how the component works.   |
| Silver | Has Performance Specs     | `aiIndex.operational.performance` is not `null`   | Performance characteristics are essential for operational readiness.         |
| Silver | Has Reliability Specs     | `aiIndex.operational.reliability` is not `null`   | Reliability requirements are crucial for setting expectations.             |
| Silver | Has Security Specs        | `aiIndex.operational.security` is not `null`      | The security profile is necessary for risk assessment.                       |
| Bronze | Has Functions Defined     | `functions` is not `null` and `functions` is not empty | A component's functions should be explicitly documented.                     |

---

## Scorecard 3: Code Mapping Health

**Identifier**: `code-mapping-health`

**Title**: Code Mapping Health

**Description**: Validates the accuracy of the "Code Beacons," ensuring that the documentation is synchronized with the source code.

### Rules

| Level  | Title                  | Statement                                                              | Rationale                                                                    |
|--------|------------------------|------------------------------------------------------------------------|------------------------------------------------------------------------------|
| Gold   | Has Repository URL     | `codeBeacons.repoUrl` is not `null` and is a valid URL                 | The link to the source code is the most fundamental code beacon.             |
| Gold   | Entry Point Exists     | The file specified in `codeBeacons.entryPoints.main` exists in the repo | A broken link to the main entry point makes the component impossible to trace. |
| Silver | Service Path Exists    | The file specified in `codeBeacons.services.primary` exists in the repo | The primary service logic should be correctly mapped.                        |
| Silver | API Routes Path Exists | The file specified in `codeBeacons.routes.api` exists in the repo      | The API routes are a critical integration point and must be correctly mapped.|
| Bronze | Test Path Exists       | The file specified in `codeBeacons.tests.unit` exists in the repo      | The location of the tests should be documented to facilitate maintenance.    |

*Note: The implementation of these rules will require custom integrations with your SCM provider (e.g., GitHub, GitLab) to check for the existence of files at the specified paths.*

---

## Scorecard 4: API Specification

**Identifier**: `api-specification`

**Title**: API Specification

**Description**: Ensures that any component providing an API has a valid and complete specification.

### Rules

| Level  | Title                     | Statement                                                              | Rationale                                                                    |
|--------|---------------------------|------------------------------------------------------------------------|------------------------------------------------------------------------------|
| Gold   | Has API Relation          | The component has a relation to an `api-spec` blueprint                | If a component provides an API, it must be explicitly related to a spec.     |
| Gold   | API Spec is Valid         | The related `api-spec` entity has a valid OpenAPI or AsyncAPI spec     | The API specification must be well-formed and parsable.                      |
| Silver | All Routes Documented     | All routes in the `api-spec` are documented with descriptions          | Undocumented routes are a source of confusion and integration errors.        |

*Note: This scorecard requires an `api-spec` blueprint for ingesting OpenAPI or AsyncAPI files.*

---

## Scorecard 5: Testing and Reliability

**Identifier**: `testing-and-reliability`

**Title**: Testing and Reliability

**Description**: Measures the component's adherence to testing standards and best practices.

### Rules

| Level  | Title                     | Statement                                                              | Rationale                                                                    |
|--------|---------------------------|------------------------------------------------------------------------|------------------------------------------------------------------------------|
| Gold   | Has Test Coverage         | The component has a `testCoverage` property greater than 0             | Every component should have at least some test coverage.                     |
| Silver | Meets Coverage Threshold  | `testCoverage` is greater than or equal to 80%                         | A high level of test coverage is a strong indicator of quality and reliability.|
| Bronze | Has Testing Scenarios     | `functions` contains at least one function with `testingScenarios` defined | Documenting testing scenarios is crucial for understanding expected behavior.  |

*Note: The `testCoverage` property would need to be ingested from a code quality tool (e.g., SonarQube, Codecov) and mapped to the component in Port.io.*
