## Table of Contents

- [Component Templates](#component-templates)
  - [Epic Assembly Template](#epic-assembly-template)
    - [Purpose](#purpose)
    - [Template Structure](#template-structure)
      - [1) Epic Definition](#1-epic-definition)
  - [Epic](#epic)
      - [2) Capabilities Decomposition](#2-capabilities-decomposition)
  - [Capabilities](#capabilities)
      - [3) Candidate Blocks Discovery](#3-candidate-blocks-discovery)
  - [Candidate Blocks](#candidate-blocks)
      - [4) Scoring Rubric Application](#4-scoring-rubric-application)
  - [Scoring Rubric](#scoring-rubric)
      - [5) Implementation Decisions](#5-implementation-decisions)
  - [Decisions](#decisions)
      - [6) Risk Assessment](#6-risk-assessment)
  - [Risks & Edge Cases](#risks-edge-cases)
      - [7) Acceptance Criteria](#7-acceptance-criteria)
  - [Acceptance Criteria](#acceptance-criteria)
      - [8) Follow-up Actions](#8-follow-up-actions)
  - [Follow-ups](#follow-ups)
  - [Component Type Templates](#component-type-templates)
    - [Service Components](#service-components)
      - [Catalog Info Template](#catalog-info-template)
- [catalog-info.yaml for services](#catalog-infoyaml-for-services)
      - [Soul Template for Services](#soul-template-for-services)
- [soul.yaml for services](#soulyaml-for-services)
    - [API Components (Contract-First)](#api-components-contract-first)
      - [Catalog Info Template](#catalog-info-template)
- [catalog-info.yaml for APIs](#catalog-infoyaml-for-apis)
      - [Soul Template for APIs](#soul-template-for-apis)
- [soul.yaml for APIs](#soulyaml-for-apis)
    - [Resource Components (Infrastructure)](#resource-components-infrastructure)
      - [Catalog Info Template](#catalog-info-template)
- [catalog-info.yaml for resources](#catalog-infoyaml-for-resources)
      - [Soul Template for Resources](#soul-template-for-resources)
- [soul.yaml for resources](#soulyaml-for-resources)
    - [Library Components](#library-components)
      - [Catalog Info Template](#catalog-info-template)
- [catalog-info.yaml for libraries](#catalog-infoyaml-for-libraries)
      - [Soul Template for Libraries](#soul-template-for-libraries)
- [soul.yaml for libraries](#soulyaml-for-libraries)
  - [Scoring Rubric Framework](#scoring-rubric-framework)
    - [Evaluation Criteria](#evaluation-criteria)
      - [1. Contract Fit (1-5)](#1-contract-fit-1-5)
      - [2. Behavior Fit (1-5)](#2-behavior-fit-1-5)
      - [3. Stability (1-5)](#3-stability-1-5)
      - [4. Security/Accessibility Fit (1-5)](#4-securityaccessibility-fit-1-5)
      - [5. Integration Cost (1-5)](#5-integration-cost-1-5)
    - [Decision Matrix](#decision-matrix)
    - [Scoring Examples](#scoring-examples)
      - [High-Score Reuse Example](#high-score-reuse-example)
      - [Refactor Candidate Example](#refactor-candidate-example)
  - [Template Customization Guidelines](#template-customization-guidelines)
    - [Adaptation Strategies](#adaptation-strategies)
      - [1. Domain-Specific Customization](#1-domain-specific-customization)
      - [2. Team Size Adaptations](#2-team-size-adaptations)
      - [3. Technology Stack Variations](#3-technology-stack-variations)
    - [Template Evolution Process](#template-evolution-process)
      - [1. Template Versioning](#1-template-versioning)
  - [Template Version History](#template-version-history)
      - [2. Feedback Integration](#2-feedback-integration)
      - [3. Quality Metrics](#3-quality-metrics)
  - [Decision Matrices for Development Approaches](#decision-matrices-for-development-approaches)
    - [Reuse vs Refactor vs New Development](#reuse-vs-refactor-vs-new-development)
      - [Decision Tree](#decision-tree)
      - [Reuse Decision Factors](#reuse-decision-factors)
      - [Refactor Decision Factors](#refactor-decision-factors)
    - [Component Type Selection Matrix](#component-type-selection-matrix)
  - [Time-Saving Tips and Automation](#time-saving-tips-and-automation)
    - [Template Generation Scripts](#template-generation-scripts)
      - [Create Component from Template](#create-component-from-template)
- [create-component.sh](#create-componentsh)
      - [Generate UUIDs and Metadata](#generate-uuids-and-metadata)
- [Generate UUID for component](#generate-uuid-for-component)
- [Generate current date in ISO format](#generate-current-date-in-iso-format)
- [Extract API endpoints from code](#extract-api-endpoints-from-code)
- [List dependencies from package.json](#list-dependencies-from-packagejson)
    - [Content Generation Helpers](#content-generation-helpers)
      - [Auto-populate Common Fields](#auto-populate-common-fields)
- [Get git repository info](#get-git-repository-info)
- [Get current branch and commit](#get-current-branch-and-commit)
- [Get team from CODEOWNERS or git config](#get-team-from-codeowners-or-git-config)
      - [Template Validation](#template-validation)
- [validate-template.sh](#validate-templatesh)
- [Check required files exist](#check-required-files-exist)
- [Validate YAML syntax](#validate-yaml-syntax)

---

# Component Templates

This document provides comprehensive templates and scoring rubrics for epic assembly, component creation, and reuse decisions. It consolidates proven patterns for decomposing epics into capabilities, evaluating existing blocks, and making informed decisions about reuse vs refactor vs new development.

## Epic Assembly Template

### Purpose
The Epic Assembly Template replaces heavyweight PRDs for small/medium epics by providing a structured approach to:
- Decompose epics into concrete capabilities
- Discover and evaluate existing blocks
- Make data-driven reuse decisions
- Document risks and acceptance criteria

### Template Structure

#### 1) Epic Definition
```markdown
## Epic
- ID: [feature:domain-capability]
- Title: [Domain - Capability description]
- Owner: [Team/Domain]
- Status: [Planning/In Progress/Complete]
```

#### 2) Capabilities Decomposition
```markdown
## Capabilities
List the concrete capabilities needed to deliver the Epic.

- [Capability 1: Specific, testable outcome]
- [Capability 2: Specific, testable outcome]
- [Capability 3: Specific, testable outcome]
- [Optional capabilities with clear conditions]
```

#### 3) Candidate Blocks Discovery
```markdown
## Candidate Blocks
Document viable existing blocks and assess them quickly.

| Block ID | Layer | Signature | Summary | Pros | Cons |
|---|---|---|---|---|---|
| [block:domain.function] | [domain/foundation/ui] | [Type signature] | [Brief description] | [Key advantages] | [Key concerns] |
| [block:domain.function] | [domain/foundation/ui] | [Type signature] | [Brief description] | [Key advantages] | [Key concerns] |
```

#### 4) Scoring Rubric Application
```markdown
## Scoring Rubric
Score 1–5 each (higher is better). Sum decides action.

Criteria: Contract Fit, Behavior Fit, Stability, Security/A11y Fit, Integration Cost

Example:
- [block.name]: [score] + [score] + [score] + [score] + [score] = [total] → [Decision]

Thresholds:
- ≥ 18: Reuse
- 14–17: Refactor (Foundation Epic)
- ≤ 13: New Block (design card required)
```

#### 5) Implementation Decisions
```markdown
## Decisions
- **Reuse:**
  - [block.name] ([layer]) - [brief rationale]
- **Refactor:**
  - [block.name] ([layer]) - [refactor scope and rationale]
- **New Blocks Needed:**
  - [new.block.name] ([layer]) - [rationale and design requirements]
```

#### 6) Risk Assessment
```markdown
## Risks & Edge Cases
- [Risk 1: Description and mitigation approach]
- [Risk 2: Description and mitigation approach]
- [Edge Case 1: Scenario and handling approach]
- [Edge Case 2: Scenario and handling approach]
```

#### 7) Acceptance Criteria
```markdown
## Acceptance Criteria
- [Capability 1]: [Specific, testable outcome with success criteria]
- [Capability 2]: [Specific, testable outcome with success criteria]
- [Quality Gates]: [Performance, security, accessibility requirements]
```

#### 8) Follow-up Actions
```markdown
## Follow-ups
- **Tests:** [Unit, integration, e2e test requirements]
- **Docs:** [Documentation updates needed]
- **CI/CD:** [Build, deployment, monitoring considerations]
- **Dependencies:** [Approved modules and footprint constraints]
```

## Component Type Templates

### Service Components

#### Entity Blueprint Template
```yaml
# [service-name].blueprint.yaml - Comprehensive Entity Blueprint for Services
apiVersion: cortex.atlas/v1
kind: ComponentBlueprint
metadata:
  name: [service-name]
  title: [Service Display Name]
  description: [Brief service description]
  type: service
  owner: team:default/[team-name]
  system: system:default/[system-name]
  lifecycle: [experimental/production]
  annotations:
    github.com/project-slug: [org/repo]

# Genesis and business context
genesis:
  problem: "[What business problem this service solves]"
  solution: "[How this service addresses the problem]"
  businessValue: "[Quantifiable business impact and value]"

# AI Index for optimal traversability
aiIndex:
  anchors:
    overview: "# [Service Display Name] Overview"
    api: "# API Endpoints"
    functions: "# Core Functions"
  dimensionalContext:
    structural:
      purpose: "[Primary business purpose and value proposition]"
      pattern: "[microservice/monolith/serverless]"
      runtime: "[node/python/java/etc]"
      framework: "[express/fastapi/spring/etc]"
    operational:
      sla:
        availability: "[99.9% uptime]"
        performance: "[< 200ms p95]"
      security: "[security requirements]"

# Core responsibilities and interfaces
responsibilities:
  - "[Core responsibility 1]"
  - "[Core responsibility 2]"

interfaces:
  rest:
    baseUrl: "[/api/v1/service-name]"
    authentication: "[bearer/basic/none]"
  events:
    publishes:
      - "[event.type.name]"
    subscribes:
      - "[event.type.name]"

# Operational profile
operationalProfile:
  performance:
    latency:
      p95: "[200ms]"
    throughput: "[requests/sec]"
  security:
    dataClassification: "[public/internal/confidential]"
    authentication: "[bearer/basic/none]"
  reliability:
    availability: "[99.9%]"
    errorRate: "[< 0.1%]"

# Code beacons for precise navigation
codeBeacons:
  entryPoints:
    main: "src/main.[ext]"
    server: "src/server.[ext]"
  endpoints:
    health: "src/routes/health.[ext]:healthCheck"
  functions:
    primaryLogic: "src/services/[ServiceName].[ext]:primaryFunction"

# Dependencies and relationships
providesApis:
  - api:default/[api-name]
consumesApis:
  - api:default/[external-api-name]
dependsOn:
  - resource:default/[resource-name]

dependencies:
  internal:
    - name: "[service-name]"
      purpose: "[Why this dependency exists]"
  external:
    - name: "[external-service]"
      purpose: "[Integration purpose]"
```

### API Components (Contract-First)

#### Entity Blueprint Template
```yaml
# [api-name].blueprint.yaml - Comprehensive Entity Blueprint for APIs
apiVersion: cortex.atlas/v1
kind: ComponentBlueprint
metadata:
  name: [api-name]
  title: [API Display Name]
  description: [API purpose and capabilities]
  type: api
  owner: team:default/[api-team]
  system: system:default/[system-name]
  lifecycle: [experimental/production]

# Genesis and business context
genesis:
  problem: "[What integration challenge this API solves]"
  solution: "[How this API enables integration]"
  businessValue: "[Consumer value and business impact]"

# AI Index for optimal traversability
aiIndex:
  anchors:
    overview: "# [API Display Name] Overview"
    endpoints: "# API Endpoints"
    schemas: "# Data Schemas"
  dimensionalContext:
    structural:
      purpose: "[API business purpose and consumer value]"
      version: "[v1/v2/etc]"
      stability: "[stable/beta/alpha]"
    operational:
      rateLimit: "[requests per minute/hour]"
      authentication: "[auth method and requirements]"

# API specification
apiSpecification:
  type: openapi
  version: "[3.0.0]"
  definition: |
    # Reference to OpenAPI specification
    $text: ./openapi.yaml

# Contract management
contract:
  version: "[v1/v2/etc]"
  stability: "[stable/beta/alpha]"
  breakingChanges: "[versioning strategy]"

# Consumer information
consumers:
  - name: "[consumer-service]"
    usage: "[how they use this API]"
    integration: "[integration pattern]"

# Design decisions and rationale
designDecisions:
  - decision: "[Design choice made]"
    rationale: "[Why this choice was made]"
    alternatives: "[What else was considered]"

# Operational profile
operationalProfile:
  performance:
    latency:
      p95: "[response time]"
    throughput: "[requests/sec]"
  security:
    authentication: "[auth method and requirements]"
    rateLimit: "[requests per minute/hour]"
  monitoring:
    keyMetrics: "[key metrics to track]"

# Code beacons for precise navigation
codeBeacons:
  specification: "openapi.yaml"
  implementation: "src/routes/[api-name].[ext]"
  schemas: "src/schemas/[api-name].[ext]"
  tests: "tests/api/[api-name].test.[ext]"
```

### Resource Components (Infrastructure)

#### Entity Blueprint Template
```yaml
# [resource-name].blueprint.yaml - Comprehensive Entity Blueprint for Resources
apiVersion: cortex.atlas/v1
kind: ResourceBlueprint
metadata:
  name: [resource-name]
  title: [Resource Display Name]
  description: [Resource purpose and capabilities]
  type: [database/queue/cache/storage]
  owner: team:default/[platform-team]
  system: system:default/[system-name]
  lifecycle: [experimental/production]

# Genesis and business context
genesis:
  problem: "[What data/infrastructure challenge this resource solves]"
  solution: "[How this resource addresses the challenge]"
  businessValue: "[Data value and operational impact]"

# AI Index for optimal traversability
aiIndex:
  anchors:
    overview: "# [Resource Display Name] Overview"
    schema: "# Data Schema"
    operations: "# Operations Guide"
  dimensionalContext:
    structural:
      purpose: "[Resource business purpose and data it manages]"
      type: "[PostgreSQL/Redis/S3/etc]"
      accessPattern: "[read/write/batch patterns]"
    operational:
      availability: "[uptime target]"
      capacity: "[storage/throughput limits]"

# Infrastructure specifications
infrastructure:
  type: "[PostgreSQL/Redis/S3/etc]"
  connection: "[connection method and port]"
  accessControl: "[VPC/public/private access rules]"
  capacity:
    storage: "[storage limits]"
    throughput: "[operations per second]"

# Operational profile
operationalProfile:
  performance:
    latency:
      read: "[read latency]"
      write: "[write latency]"
    throughput: "[operations/sec]"
  reliability:
    availability: "[uptime target]"
    durability: "[data durability guarantee]"
  security:
    encryption: "[at rest/in transit]"
    accessControl: "[authentication/authorization]"

# Operations and maintenance
operations:
  backupPolicy: "[backup frequency and retention]"
  monitoring: "[key metrics and alerts]"
  scaling: "[scaling approach and limits]"
  maintenance: "[maintenance windows and procedures]"

# Data schema (for databases)
schema:
  tables:
    - name: "[table_name]"
      purpose: "[what data this stores]"
      primaryKey: "[primary key definition]"
  indexes:
    - name: "[index_name]"
      purpose: "[query optimization purpose]"
      columns: "[indexed columns]"

# Consumer services and access patterns
consumers:
  - service: "[service-name]"
    accessPattern: "[read/write/batch patterns]"
    usage: "[how the service uses this resource]"

# Code beacons for configuration and management
codeBeacons:
  configuration: "config/[resource-name].[ext]"
  migrations: "migrations/[resource-name]/"
  schemas: "schemas/[resource-name].[ext]"
  monitoring: "monitoring/[resource-name].[ext]"
```

### Library Components

#### Entity Blueprint Template
```yaml
# [library-name].blueprint.yaml - Comprehensive Entity Blueprint for Libraries
apiVersion: cortex.atlas/v1
kind: ComponentBlueprint
metadata:
  name: [library-name]
  title: [Library Display Name]
  description: [Library purpose and functionality]
  type: library
  owner: team:default/[platform-team]
  system: system:default/[system-name]
  lifecycle: [experimental/production]

# Genesis and business context
genesis:
  problem: "[What code duplication/complexity this library solves]"
  solution: "[How this library provides reusable functionality]"
  businessValue: "[Development efficiency and consistency gains]"

# AI Index for optimal traversability
aiIndex:
  anchors:
    overview: "# [Library Display Name] Overview"
    api: "# API Reference"
    examples: "# Usage Examples"
  dimensionalContext:
    structural:
      purpose: "[Library purpose and reusable functionality]"
      scope: "[functional scope and boundaries]"
    operational:
      testCoverage: "[coverage percentage target]"
      bundleSize: "[size constraints]"

# API exports and interfaces
api:
  exports:
    - name: "[function/class name]"
      purpose: "[what this export does]"
      signature: "[type signature or interface]"
      parameters: "[parameter descriptions]"
      returns: "[return value description]"

# Function specifications with rich context
functions:
  "[primaryFunction]":
    signature: "[function signature]"
    description: "[detailed function description]"
    usageExample: |
      [code example showing usage]
    parameters:
      - name: "[param1]"
        type: "[type]"
        description: "[parameter description]"
    returns:
      type: "[return type]"
      description: "[return value description]"

# Usage patterns and examples
usagePatterns:
  - pattern: "[common usage pattern]"
    example: |
      [code example]
    description: "[when to use this pattern]"

# Dependencies and peer dependencies
dependencies:
  peerDependencies:
    - name: "[peer-dep-name]"
      version: "[version constraint]"
      rationale: "[why this is a peer dependency]"
  runtime:
    - name: "[runtime-dep]"
      version: "[version]"
      purpose: "[why this dependency is needed]"

# Operational profile
operationalProfile:
  performance:
    bundleSize: "[size constraints]"
    loadTime: "[initialization time]"
  compatibility:
    browserSupport: "[supported browsers/versions]"
    nodeVersion: "[supported Node.js versions]"
  quality:
    testCoverage: "[coverage percentage target]"
    documentation: "[documentation completeness]"

# Code beacons for precise navigation
codeBeacons:
  entryPoints:
    main: "src/index.[ext]"
    types: "src/types.[ext]"
  functions:
    "[primaryFunction]": "src/[module].[ext]:[functionName]"
  tests:
    unit: "tests/unit/[library-name].test.[ext]"
    integration: "tests/integration/[library-name].test.[ext]"
  documentation:
    api: "docs/api.md"
    examples: "docs/examples.md"
```

## Scoring Rubric Framework

### Evaluation Criteria

#### 1. Contract Fit (1-5)
- **5**: Perfect interface match, no adaptation needed
- **4**: Minor interface differences, simple adapter possible
- **3**: Moderate differences, requires wrapper or configuration
- **2**: Significant differences, substantial adaptation required
- **1**: Poor fit, major interface changes needed

#### 2. Behavior Fit (1-5)
- **5**: Exact behavior match for all use cases
- **4**: Covers 90%+ of use cases, minor gaps acceptable
- **3**: Covers core use cases, some customization needed
- **2**: Partial behavior match, significant customization required
- **1**: Behavior mismatch, extensive modification needed

#### 3. Stability (1-5)
- **5**: Production-proven, stable API, comprehensive tests
- **4**: Well-tested, minor version changes only
- **3**: Generally stable, occasional breaking changes
- **2**: Some instability, moderate breaking changes
- **1**: Unstable, frequent breaking changes

#### 4. Security/Accessibility Fit (1-5)
- **5**: Exceeds security/a11y requirements
- **4**: Meets all requirements with minor enhancements
- **3**: Meets core requirements, some gaps to address
- **2**: Partial compliance, significant work needed
- **1**: Poor compliance, major security/a11y concerns

#### 5. Integration Cost (1-5)
- **5**: Drop-in replacement, minimal integration effort
- **4**: Simple integration, well-documented process
- **3**: Moderate integration complexity, clear path forward
- **2**: Complex integration, significant effort required
- **1**: Very complex integration, high risk and effort

### Decision Matrix

| Total Score | Decision | Action Required |
|-------------|----------|-----------------|
| 22-25 | **Reuse (Ideal)** | Use as-is with minimal documentation updates |
| 18-21 | **Reuse (Good)** | Use with minor adaptations or configuration |
| 14-17 | **Refactor** | Create Foundation Epic to improve reusability |
| 10-13 | **New Block** | Design new component, consider design card |
| 5-9 | **Avoid** | Do not use, create new implementation |

### Scoring Examples

#### High-Score Reuse Example
```markdown
Block: auth.validateToken
- Contract Fit: 5 (perfect JWT interface match)
- Behavior Fit: 4 (covers all use cases, minor config needed)
- Stability: 5 (production-proven, stable API)
- Security Fit: 5 (exceeds security requirements)
- Integration Cost: 5 (drop-in replacement)
Total: 24 → Reuse (Ideal)
```

#### Refactor Candidate Example
```markdown
Block: legacy.userService
- Contract Fit: 3 (requires adapter layer)
- Behavior Fit: 4 (good functionality, needs modernization)
- Stability: 3 (stable but outdated patterns)
- Security Fit: 2 (needs security updates)
- Integration Cost: 3 (moderate effort to integrate)
Total: 15 → Refactor (Foundation Epic needed)
```

## Template Customization Guidelines

### Adaptation Strategies

#### 1. Domain-Specific Customization
- **Financial Services**: Add compliance and audit fields
- **Healthcare**: Include HIPAA and privacy considerations
- **E-commerce**: Add performance and scalability metrics
- **IoT/Hardware**: Include device compatibility and constraints

#### 2. Team Size Adaptations
- **Small Teams**: Simplified templates with essential fields only
- **Large Teams**: Extended templates with detailed governance
- **Distributed Teams**: Enhanced communication and handoff sections

#### 3. Technology Stack Variations
- **Microservices**: Emphasize service boundaries and communication
- **Monoliths**: Focus on module boundaries and internal APIs
- **Serverless**: Include cold start and resource constraints
- **Mobile**: Add platform-specific considerations

### Template Evolution Process

#### 1. Template Versioning
```markdown
## Template Version History
- v1.0: Initial template creation
- v1.1: Added security scoring criteria
- v1.2: Enhanced API contract templates
- v2.0: Major restructure for microservices
```

#### 2. Feedback Integration
- Collect usage feedback after each epic completion
- Track common customizations and pain points
- Regular template review sessions with teams
- Version control template changes with rationale

#### 3. Quality Metrics
- Template adoption rate across teams
- Time savings compared to ad-hoc approaches
- Quality of resulting component documentation
- Reduction in rework and refactoring needs

## Decision Matrices for Development Approaches

### Reuse vs Refactor vs New Development

#### Decision Tree
```
Is there an existing block that addresses this capability?
├─ No → Create New Block
└─ Yes → Score the block (1-25)
   ├─ Score ≥ 18 → Reuse
   ├─ Score 14-17 → Refactor
   └─ Score ≤ 13 → Create New Block
```

#### Reuse Decision Factors
| Factor | Reuse Favored | New Development Favored |
|--------|---------------|-------------------------|
| **Time Pressure** | High (quick delivery) | Low (can invest in perfect solution) |
| **Uniqueness** | Common pattern | Novel requirements |
| **Team Expertise** | Limited domain knowledge | Deep domain expertise |
| **Quality Requirements** | Standard quality acceptable | Exceptional quality needed |
| **Maintenance Burden** | Shared maintenance preferred | Team wants full control |

#### Refactor Decision Factors
| Factor | Refactor Favored | Alternative Favored |
|--------|------------------|---------------------|
| **Block Usage** | Multiple consumers | Single consumer (reuse as-is) |
| **Technical Debt** | High debt, worth fixing | Low debt (reuse) or unfixable (new) |
| **Team Capacity** | Available for foundation work | Limited capacity (reuse or new) |
| **Strategic Value** | High reuse potential | Low reuse potential |

### Component Type Selection Matrix

| Use Case | Service | API | Library | Resource |
|----------|---------|-----|---------|----------|
| **Business Logic** | ✅ Primary | ⚠️ Contract only | ❌ No state | ❌ No logic |
| **Data Storage** | ⚠️ Via resource | ❌ No storage | ❌ No storage | ✅ Primary |
| **Reusable Functions** | ❌ Too heavy | ❌ No implementation | ✅ Primary | ❌ No logic |
| **External Interface** | ⚠️ Implementation | ✅ Primary | ❌ No interface | ❌ No interface |
| **Infrastructure** | ❌ Too high-level | ❌ No infrastructure | ❌ No infrastructure | ✅ Primary |

## Time-Saving Tips and Automation

### Template Generation Scripts

#### Create Component from Template
```bash
#!/bin/bash
# create-component.sh
COMPONENT_TYPE=$1
COMPONENT_NAME=$2
TEMPLATE_DIR="templates/$COMPONENT_TYPE"

if [ -d "$TEMPLATE_DIR" ]; then
    cp -r "$TEMPLATE_DIR" "$COMPONENT_NAME"
    # Replace placeholders
    find "$COMPONENT_NAME" -type f -exec sed -i "s/\[component-name\]/$COMPONENT_NAME/g" {} \;
    find "$COMPONENT_NAME" -type f -exec sed -i "s/\[date\]/$(date -I)/g" {} \;
    echo "Created $COMPONENT_TYPE component: $COMPONENT_NAME"
else
    echo "Template not found: $TEMPLATE_DIR"
fi
```

#### Generate UUIDs and Metadata
```bash
# Generate UUID for component
uuidgen | tr '[:upper:]' '[:lower:]'

# Generate current date in ISO format
date -I

# Extract API endpoints from code
grep -r "@Route\|@Get\|@Post\|@Put\|@Delete" src/ | \
  sed 's/.*@\([A-Z][a-z]*\).*[("'"'"']\([^"'"'"']*\)[)"'"'"'].*/\1 \2/' | \
  sort | uniq

# List dependencies from package.json
jq -r '.dependencies | keys[]' package.json | sort
```

### Content Generation Helpers

#### Auto-populate Common Fields
```bash
# Get git repository info
REPO_URL=$(git remote get-url origin)
REPO_SLUG=$(echo $REPO_URL | sed 's/.*[:/]\([^/]*\/[^/]*\)\.git/\1/')

# Get current branch and commit
BRANCH=$(git branch --show-current)
COMMIT=$(git rev-parse --short HEAD)

# Get team from CODEOWNERS or git config
TEAM=$(grep -h "^[^#]" .github/CODEOWNERS | head -1 | awk '{print $NF}' | sed 's/@//')
```

#### Template Validation
```bash
#!/bin/bash
# validate-template.sh
COMPONENT_DIR=$1

# Check required files exist
REQUIRED_FILES=("*.blueprint.yaml")
for pattern in "${REQUIRED_FILES[@]}"; do
    if ! ls "$COMPONENT_DIR"/$pattern 1> /dev/null 2>&1; then
        echo "❌ Missing required Entity Blueprint file: $pattern"
        exit 1
    fi
done

# Validate Entity Blueprint YAML syntax and structure
for blueprint_file in "$COMPONENT_DIR"/*.blueprint.yaml; do
    if ! yq eval . "$blueprint_file" > /dev/null 2>&1; then
        echo "❌ Invalid YAML syntax: $blueprint_file"
        exit 1
    fi

    # Validate required Entity Blueprint sections
    if ! yq eval '.metadata.name' "$blueprint_file" > /dev/null 2>&1; then
        echo "❌ Missing metadata.name in: $blueprint_file"
        exit 1
    fi

    if ! yq eval '.genesis' "$blueprint_file" > /dev/null 2>&1; then
        echo "❌ Missing genesis section in: $blueprint_file"
        exit 1
    fi
done

echo "✅ Template validation passed"
```

This comprehensive template system provides structured approaches for epic assembly, component creation, and reuse decisions while maintaining flexibility for team-specific adaptations and automation opportunities.