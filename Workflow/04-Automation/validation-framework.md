## Table of Contents

- [Validation Framework](#validation-framework)
  - [Overview](#overview)
  - [Problem Statement](#problem-statement)
  - [Three-Level Validation Architecture](#three-level-validation-architecture)
    - [Level 1: Syntax Validation](#level-1-syntax-validation)
      - [Implementation](#implementation)
- [validate-yaml-syntax.sh](#validate-yaml-syntaxsh)
- [Check YAML syntax](#check-yaml-syntax)
- [Check JSON in markdown files](#check-json-in-markdown-files)
- [Check required files exist](#check-required-files-exist)
- [Validate that file paths in Code Mapping sections exist](#validate-that-file-paths-in-code-mapping-sections-exist)
- [Extract file paths from documentation](#extract-file-paths-from-documentation)
    - [Level 2: Semantic Validation](#level-2-semantic-validation)
      - [Implementation](#implementation)
    - [Level 3: Business Validation](#level-3-business-validation)
      - [Implementation](#implementation)
  - [Business Validation Checklist](#business-validation-checklist)
    - [Documentation Completeness](#documentation-completeness)
    - [Stakeholder Alignment](#stakeholder-alignment)
    - [Quality Standards](#quality-standards)
  - [CI/CD Integration](#cicd-integration)
    - [GitHub Actions Workflow](#github-actions-workflow)
    - [GitLab CI Pipeline](#gitlab-ci-pipeline)
  - [Pre-commit Hooks](#pre-commit-hooks)
    - [Installation and Configuration](#installation-and-configuration)
- [Install pre-commit](#install-pre-commit)
- [Install hooks](#install-hooks)
- [Run hooks on all files (optional)](#run-hooks-on-all-files-optional)
  - [Quality Metrics](#quality-metrics)
    - [Documentation Health Dashboard](#documentation-health-dashboard)
  - [Troubleshooting Guide](#troubleshooting-guide)
    - [Common Validation Errors](#common-validation-errors)
      - [1. Missing Reference Errors](#1-missing-reference-errors)
- [Check if the referenced entity exists](#check-if-the-referenced-entity-exists)
- [Check for typos in the reference](#check-for-typos-in-the-reference)
- [Verify the entity kind is correct](#verify-the-entity-kind-is-correct)
      - [2. Code Mapping Reference Errors](#2-code-mapping-reference-errors)
- [Find where the file might have moved](#find-where-the-file-might-have-moved)
- [Check git history for file moves](#check-git-history-for-file-moves)
- [Update documentation with correct path](#update-documentation-with-correct-path)
      - [3. YAML Syntax Errors](#3-yaml-syntax-errors)
- [Check YAML syntax](#check-yaml-syntax)
- [Common issues:](#common-issues)
- [- Incorrect indentation](#incorrect-indentation)
- [- Missing quotes around special characters](#missing-quotes-around-special-characters)
- [- Invalid list formatting](#invalid-list-formatting)
- [Fix indentation](#fix-indentation)
      - [4. Missing Required Fields](#4-missing-required-fields)
- [Generate UUID for missing entities](#generate-uuid-for-missing-entities)
- [Add missing UUID to soul.yaml](#add-missing-uuid-to-soulyaml)
    - [Validation Bypass (Emergency Only)](#validation-bypass-emergency-only)
- [Skip pre-commit hooks (emergency only)](#skip-pre-commit-hooks-emergency-only)
- [Skip CI validation (requires admin privileges)](#skip-ci-validation-requires-admin-privileges)
    - [Debugging Validation Scripts](#debugging-validation-scripts)
- [Enable verbose output](#enable-verbose-output)
- [Run individual validation steps](#run-individual-validation-steps)
- [Check script permissions](#check-script-permissions)
- [Verify dependencies](#verify-dependencies)
- [Test with single entity](#test-with-single-entity)
- [Generate detailed logs](#generate-detailed-logs)
  - [Configuration](#configuration)
    - [Environment Variables](#environment-variables)
- [Validation configuration](#validation-configuration)
    - [Validation Configuration File](#validation-configuration-file)

---

# Validation Framework

## Overview

The Cortex methodology validation framework provides comprehensive automated validation to ensure code-documentation synchronization, maintain system integrity, and enforce quality standards throughout the development lifecycle. This framework implements a three-level validation architecture that catches issues early, provides clear feedback, and maintains living documentation that stays current with the codebase.

## Problem Statement

Without automated detection of code-documentation drift, the methodology relies entirely on developer discipline to maintain synchronization. This creates several critical risks:

- **Silent Documentation Rot**: Code changes without corresponding documentation updates
- **Inconsistent Enforcement**: Some developers update docs, others don't
- **Late Discovery**: Documentation drift discovered during onboarding or incidents
- **Review Overhead**: Manual verification of code-doc consistency in every PR
- **System Integrity Issues**: Broken references, missing dependencies, invalid configurations
- **Quality Degradation**: Gradual decline in documentation completeness and accuracy

## Three-Level Validation Architecture

The validation framework implements a progressive validation approach that catches different types of issues at appropriate stages:

```
Level 1: Syntax Validation (Automated - Pre-commit)
    ↓
Level 2: Semantic Validation (Automated - CI/CD)
    ↓  
Level 3: Business Validation (Manual Review - PR Process)
```

### Level 1: Syntax Validation

**Purpose**: Catch basic format and structure errors before they enter the repository

**When**: Pre-commit hooks, local development

**What it checks**: 
- File format compliance (YAML, JSON, Markdown)
- Required file presence
- Basic structure validation
- Code mapping reference syntax

#### Implementation

**Pre-commit Configuration**: `.pre-commit-config.yaml`
```yaml
repos:
  - repo: local
    hooks:
      - id: validate-code-mapping
        name: Validate Code Mapping References
        entry: scripts/validate-code-mapping.sh
        language: script
        files: '^docs/.*\.md$'
        
      - id: check-yaml-syntax
        name: Check YAML Syntax
        entry: scripts/validate-yaml-syntax.sh
        language: script
        files: '^.*\.(yaml|yml)$'
        
      - id: validate-required-files
        name: Validate Required Files
        entry: scripts/validate-required-files.sh
        language: script
        files: '^(entities|docs)/.*'
```

**Syntax Validation Script**: `scripts/validate-yaml-syntax.sh`
```bash
#!/bin/bash
# validate-yaml-syntax.sh

set -e

ERRORS=0

echo "🔍 Validating YAML syntax..."

# Check YAML syntax
for file in $(find entities -name "*.yaml" -o -name "*.yml"); do
  echo "Checking $file..."
  if ! yamllint "$file"; then
    echo "❌ YAML syntax error in $file"
    ((ERRORS++))
  fi
done

# Check JSON in markdown files
for file in $(find entities -name "index.md"); do
  echo "Checking JSON blocks in $file..."
  # Extract JSON blocks and validate
  grep -n '```json' "$file" | while read -r line_info; do
    line_num=$(echo "$line_info" | cut -d: -f1)
    json_block=$(sed -n "${line_num},/```/p" "$file" | sed '1d;$d')
    
    if ! echo "$json_block" | jq empty 2>/dev/null; then
      echo "❌ Invalid JSON block at line $line_num in $file"
      ((ERRORS++))
    fi
  done
done

# Check required files exist
for dir in entities/*/*/; do
  if [ -d "$dir" ]; then
    for required in catalog-info.yaml soul.yaml index.md; do
      if [ ! -f "$dir/$required" ]; then
        echo "❌ Missing $required in $dir"
        ((ERRORS++))
      fi
    done
  fi
done

if [ $ERRORS -eq 0 ]; then
  echo "✅ All syntax validation checks passed"
else
  echo "❌ Found $ERRORS syntax errors"
  exit 1
fi
```

**Code Mapping Validation**: `scripts/validate-code-mapping.sh`
```bash
#!/bin/bash
# Validate that file paths in Code Mapping sections exist

set -e

echo "🔍 Validating code mapping references..."

ERRORS=0

# Extract file paths from documentation
find docs -name "*.md" -exec grep -l "Code Mapping" {} \; | while read -r doc_file; do
    echo "Checking $doc_file..."
    
    # Extract file paths from code mapping tables
    grep -oP '`src/[^`]+`' "$doc_file" | sed 's/`//g' | while read -r file_path; do
        # Remove line numbers if present (e.g., src/file.ts:1-50 → src/file.ts)
        clean_path=$(echo "$file_path" | sed 's/:[0-9-]*$//')
        
        if [[ ! -f "$clean_path" ]]; then
            echo "❌ ERROR: Referenced file does not exist: $file_path in $doc_file"
            ((ERRORS++))
        fi
    done
done

if [ $ERRORS -eq 0 ]; then
  echo "✅ All code mapping references validated"
else
  echo "❌ Found $ERRORS code mapping errors"
  exit 1
fi
```

### Level 2: Semantic Validation

**Purpose**: Validate logical consistency, relationships, and system integrity

**When**: CI/CD pipeline, pull request validation

**What it checks**:
- Entity references and relationships
- API documentation currency
- Event schema consistency
- Database schema alignment
- Cross-reference integrity

#### Implementation

**Semantic Validation Engine**: `scripts/validate-semantic.js`
```javascript
#!/usr/bin/env node
// validate-semantic.js
const fs = require('fs');
const yaml = require('js-yaml');
const path = require('path');

class CortexValidator {
  constructor(basePath) {
    this.basePath = basePath;
    this.entities = new Map();
    this.errors = [];
    this.warnings = [];
  }

  // Load all entities
  loadEntities() {
    console.log('🔍 Loading entities...');
    const entityDirs = this.findEntityDirs(this.basePath);
    
    entityDirs.forEach(dir => {
      const catalogPath = path.join(dir, 'catalog-info.yaml');
      if (fs.existsSync(catalogPath)) {
        try {
          const catalog = yaml.load(fs.readFileSync(catalogPath, 'utf8'));
          const entityRef = `${catalog.kind}:${catalog.metadata.name}`;
          this.entities.set(entityRef, {
            path: dir,
            catalog,
            soul: this.loadSoul(dir),
            index: this.loadIndex(dir)
          });
        } catch (error) {
          this.errors.push({
            entity: dir,
            type: 'load-error',
            message: `Failed to load catalog-info.yaml: ${error.message}`
          });
        }
      }
    });
    
    console.log(`📋 Loaded ${this.entities.size} entities`);
  }

  loadSoul(dir) {
    const soulPath = path.join(dir, 'soul.yaml');
    if (fs.existsSync(soulPath)) {
      try {
        return yaml.load(fs.readFileSync(soulPath, 'utf8'));
      } catch (error) {
        this.errors.push({
          entity: dir,
          type: 'soul-parse-error',
          message: `Failed to parse soul.yaml: ${error.message}`
        });
      }
    }
    return null;
  }

  loadIndex(dir) {
    const indexPath = path.join(dir, 'index.md');
    if (fs.existsSync(indexPath)) {
      const content = fs.readFileSync(indexPath, 'utf8');
      return {
        content,
        aiContext: this.extractAIContext(content),
        codeMapping: this.extractCodeMapping(content)
      };
    }
    return null;
  }

  extractAIContext(content) {
    const aiContextMatch = content.match(/## AI Context Header\s*([\s\S]*?)(?=\n##|\n---|\n$)/);
    return aiContextMatch ? aiContextMatch[1].trim() : null;
  }

  extractCodeMapping(content) {
    const codeMappingMatch = content.match(/## Code Mapping\s*([\s\S]*?)(?=\n##|\n---|\n$)/);
    return codeMappingMatch ? codeMappingMatch[1].trim() : null;
  }

  findEntityDirs(basePath) {
    const dirs = [];
    
    function traverse(currentPath) {
      const items = fs.readdirSync(currentPath, { withFileTypes: true });
      
      items.forEach(item => {
        if (item.isDirectory()) {
          const fullPath = path.join(currentPath, item.name);
          
          // Check if this directory contains Entity Blueprint files
          const blueprintFiles = fs.readdirSync(fullPath).filter(file => file.endsWith('.blueprint.yaml'));
          if (blueprintFiles.length > 0) {
            dirs.push(fullPath);
          } else {
            // Continue traversing subdirectories
            traverse(fullPath);
          }
        }
      });
    }
    
    if (fs.existsSync(basePath)) {
      traverse(basePath);
    }
    
    return dirs;
  }

  // Validate all references
  validateReferences() {
    console.log('🔍 Validating entity references...');
    
    this.entities.forEach((entity, ref) => {
      // Check consumesApis references
      if (entity.catalog.spec?.consumesApis) {
        entity.catalog.spec.consumesApis.forEach(apiRef => {
          const normalizedRef = this.normalizeRef(apiRef);
          if (!this.entities.has(normalizedRef)) {
            this.errors.push({
              entity: ref,
              type: 'missing-reference',
              message: `References non-existent API: ${apiRef}`
            });
          }
        });
      }

      // Check dependencies
      if (entity.catalog.spec?.dependsOn) {
        entity.catalog.spec.dependsOn.forEach(depRef => {
          const normalizedRef = this.normalizeRef(depRef);
          if (!this.entities.has(normalizedRef)) {
            this.errors.push({
              entity: ref,
              type: 'missing-dependency',
              message: `Depends on non-existent entity: ${depRef}`
            });
          }
        });
      }

      // Check system membership
      if (entity.catalog.spec?.system) {
        const systemRef = this.normalizeRef(entity.catalog.spec.system);
        if (!this.entities.has(systemRef)) {
          this.errors.push({
            entity: ref,
            type: 'missing-system',
            message: `References non-existent system: ${entity.catalog.spec.system}`
          });
        }
      }
    });
  }

  // Validate required fields
  validateRequiredFields() {
    console.log('🔍 Validating required fields...');
    
    this.entities.forEach((entity, ref) => {
      // Check soul.yaml required fields
      if (!entity.soul?.entityMetadata?.uuid) {
        this.errors.push({
          entity: ref,
          type: 'missing-field',
          message: 'soul.yaml missing entityMetadata.uuid'
        });
      }

      if (!entity.soul?.entityMetadata?.name) {
        this.errors.push({
          entity: ref,
          type: 'missing-field',
          message: 'soul.yaml missing entityMetadata.name'
        });
      }

      // Check AI Context Header
      if (!entity.index?.aiContext) {
        this.warnings.push({
          entity: ref,
          type: 'missing-ai-context',
          message: 'index.md missing AI Context Header'
        });
      }

      // Check Code Mapping section
      if (!entity.index?.codeMapping) {
        this.warnings.push({
          entity: ref,
          type: 'missing-code-mapping',
          message: 'index.md missing Code Mapping section'
        });
      }

      // Validate catalog-info.yaml required fields
      if (!entity.catalog.metadata?.name) {
        this.errors.push({
          entity: ref,
          type: 'missing-catalog-field',
          message: 'catalog-info.yaml missing metadata.name'
        });
      }

      if (!entity.catalog.metadata?.description) {
        this.warnings.push({
          entity: ref,
          type: 'missing-description',
          message: 'catalog-info.yaml missing metadata.description'
        });
      }
    });
  }

  normalizeRef(ref) {
    // Handle different reference formats
    if (ref.includes(':')) {
      return ref;
    }
    // Assume component if no kind specified
    return `component:${ref}`;
  }

  // Run all validations
  validate() {
    this.loadEntities();
    this.validateReferences();
    this.validateRequiredFields();
    
    return {
      errors: this.errors,
      warnings: this.warnings,
      entityCount: this.entities.size
    };
  }
}

// Run validation
const validator = new CortexValidator('./entities');
const results = validator.validate();

console.log(`\n📊 Validation Results:`);
console.log(`  Entities processed: ${results.entityCount}`);
console.log(`  Errors: ${results.errors.length}`);
console.log(`  Warnings: ${results.warnings.length}`);

if (results.errors.length > 0) {
  console.log('\n❌ Validation errors found:');
  results.errors.forEach(err => {
    console.log(`  ${err.entity}: ${err.message}`);
  });
}

if (results.warnings.length > 0) {
  console.log('\n⚠️  Validation warnings:');
  results.warnings.forEach(warn => {
    console.log(`  ${warn.entity}: ${warn.message}`);
  });
}

if (results.errors.length > 0) {
  process.exit(1);
} else {
  console.log('\n✅ All semantic validations passed!');
}
```

**API Documentation Currency Check**: `scripts/check-api-docs.js`
```javascript
#!/usr/bin/env node
// Check if API changes require documentation updates

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Checking API documentation currency...');

// Get changed files in this commit
let changedFiles = [];
try {
  changedFiles = execSync('git diff --name-only HEAD~1 HEAD', { encoding: 'utf-8' })
    .trim()
    .split('\n')
    .filter(file => file.length > 0);
} catch (error) {
  console.log('⚠️  Could not get git diff, checking all files');
  // Fallback to checking all files if git diff fails
  changedFiles = [];
}

const apiFiles = changedFiles.filter(file => 
  file.includes('routes/') || 
  file.includes('api/') || 
  file.includes('controllers/') ||
  file.includes('endpoints/') ||
  file.endsWith('.api.ts') ||
  file.endsWith('.controller.ts')
);

if (apiFiles.length === 0) {
  console.log('✅ No API files changed');
  process.exit(0);
}

console.log(`📋 API files changed: ${apiFiles.join(', ')}`);

// Check if corresponding documentation was updated
const docFiles = changedFiles.filter(file => 
  file.includes('docs/') && file.endsWith('.md') ||
  file.includes('entities/') && file.endsWith('index.md')
);

if (docFiles.length === 0) {
  console.log('❌ ERROR: API files changed but no documentation updated');
  console.log('💡 Consider updating relevant component documentation');
  console.log('💡 Update API specifications in component index.md files');
  process.exit(1);
}

console.log(`📚 Documentation files also changed: ${docFiles.join(', ')}`);
console.log('✅ Documentation files also changed - good practice!');
```

### Level 3: Business Validation

**Purpose**: Ensure business logic accuracy, documentation completeness, and stakeholder alignment

**When**: Pull request review process, manual quality gates

**What it checks**:
- Business logic accuracy
- Documentation completeness and clarity
- Stakeholder requirements alignment
- User experience considerations
- Security and compliance requirements

#### Implementation

**Business Validation Checklist**:
```markdown
## Business Validation Checklist

### Documentation Completeness
- [ ] All user-facing features documented
- [ ] Business rules clearly explained
- [ ] Error scenarios and handling documented
- [ ] Performance characteristics documented
- [ ] Security considerations addressed

### Stakeholder Alignment
- [ ] Requirements traceability maintained
- [ ] Acceptance criteria met
- [ ] User experience considerations documented
- [ ] Integration points clearly defined

### Quality Standards
- [ ] Documentation follows style guide
- [ ] Examples are complete and tested
- [ ] Cross-references are accurate
- [ ] Terminology is consistent
```

## CI/CD Integration

### GitHub Actions Workflow

**Complete Validation Pipeline**: `.github/workflows/validate-documentation.yml`
```yaml
name: Documentation Validation

on:
  pull_request:
    paths:
      - 'src/**'
      - 'docs/**'
      - 'entities/**'
      - 'migrations/**'
  push:
    branches: [main, develop]

jobs:
  validate-docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 2  # Need previous commit for comparison
          
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: |
          npm install -g yamllint jq js-yaml
          npm install
        
      - name: Level 1 - Syntax Validation
        run: |
          echo "🔍 Running Level 1 - Syntax Validation"
          ./scripts/validate-yaml-syntax.sh
          ./scripts/validate-code-mapping.sh
          ./scripts/validate-required-files.sh
        
      - name: Level 2 - Semantic Validation
        run: |
          echo "🔍 Running Level 2 - Semantic Validation"
          ./scripts/validate-semantic.js
          ./scripts/check-api-docs.js
          ./scripts/validate-event-schemas.js
          ./scripts/validate-db-schemas.js
        
      - name: Generate Validation Report
        run: ./scripts/generate-validation-report.js
        
      - name: Comment PR with Results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const reportPath = './validation-report.json';
            
            if (fs.existsSync(reportPath)) {
              const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
              
              const statusEmoji = report.success ? '✅' : '❌';
              const warningEmoji = report.warnings.length > 0 ? '⚠️' : '';
              
              const comment = `
              ## ${statusEmoji} Documentation Validation Report ${warningEmoji}
              
              | Validation Level | Status | Issues |
              |------------------|--------|---------|
              | Level 1 - Syntax | ${report.level1.status} | ${report.level1.errors.length} errors |
              | Level 2 - Semantic | ${report.level2.status} | ${report.level2.errors.length} errors, ${report.level2.warnings.length} warnings |
              
              ### Summary
              - **Entities Processed**: ${report.entityCount}
              - **Total Errors**: ${report.totalErrors}
              - **Total Warnings**: ${report.totalWarnings}
              
              ${report.errors.length > 0 ? '### ❌ Errors:\n' + report.errors.map(e => `- **${e.entity}**: ${e.message}`).join('\n') : ''}
              ${report.warnings.length > 0 ? '### ⚠️ Warnings:\n' + report.warnings.map(w => `- **${w.entity}**: ${w.message}`).join('\n') : ''}
              
              ${report.success ? 
                '🎉 All validation checks passed! Ready for Level 3 business validation.' : 
                '❌ Validation failed. Please fix errors before proceeding.'}
              `;
              
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comment
              });
            }
```

### GitLab CI Pipeline

**GitLab CI Configuration**: `.gitlab-ci.yml`
```yaml
stages:
  - validate-syntax
  - validate-semantic
  - generate-report

variables:
  NODE_VERSION: "18"

validate-syntax:
  stage: validate-syntax
  image: node:${NODE_VERSION}
  before_script:
    - apt-get update && apt-get install -y yamllint jq
    - npm install -g js-yaml
  script:
    - echo "🔍 Running Level 1 - Syntax Validation"
    - ./scripts/validate-yaml-syntax.sh
    - ./scripts/validate-code-mapping.sh
    - ./scripts/validate-required-files.sh
  artifacts:
    reports:
      junit: syntax-validation-report.xml
    when: always

validate-semantic:
  stage: validate-semantic
  image: node:${NODE_VERSION}
  dependencies:
    - validate-syntax
  script:
    - echo "🔍 Running Level 2 - Semantic Validation"
    - npm install
    - ./scripts/validate-semantic.js
    - ./scripts/check-api-docs.js
  artifacts:
    reports:
      junit: semantic-validation-report.xml
    when: always

generate-report:
  stage: generate-report
  image: node:${NODE_VERSION}
  dependencies:
    - validate-syntax
    - validate-semantic
  script:
    - ./scripts/generate-validation-report.js
  artifacts:
    reports:
      junit: validation-report.xml
    paths:
      - validation-report.json
      - validation-report.html
    when: always
```

## Pre-commit Hooks

### Installation and Configuration

**Install pre-commit hooks**:
```bash
# Install pre-commit
pip install pre-commit

# Install hooks
pre-commit install

# Run hooks on all files (optional)
pre-commit run --all-files
```

**Advanced Pre-commit Configuration**: `.pre-commit-config.yaml`
```yaml
repos:
  - repo: local
    hooks:
      - id: validate-code-mapping
        name: Validate Code Mapping References
        entry: scripts/validate-code-mapping.sh
        language: script
        files: '^docs/.*\.md$'
        
      - id: check-yaml-syntax
        name: Check YAML Syntax
        entry: scripts/validate-yaml-syntax.sh
        language: script
        files: '^.*\.(yaml|yml)$'
        
      - id: validate-required-files
        name: Validate Required Files
        entry: scripts/validate-required-files.sh
        language: script
        files: '^(entities|docs)/.*'
        
      - id: check-api-documentation
        name: Check API Documentation Currency
        entry: scripts/check-api-docs.js
        language: node
        files: '^(src/.*\.(ts|js)|docs/.*\.md|entities/.*/index\.md)$'
        
      - id: validate-json-schemas
        name: Validate JSON Schemas in Documentation
        entry: scripts/validate-json-schemas.js
        language: node
        files: '^.*\.md$'

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-merge-conflict
```

## Quality Metrics

### Documentation Health Dashboard

**Metrics Collection Script**: `scripts/collect-metrics.js`
```javascript
#!/usr/bin/env node
// Collect documentation quality metrics

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');

class DocumentationMetrics {
  constructor() {
    this.metrics = {
      coverage: {
        totalEntities: 0,
        entitiesWithDocs: 0,
        entitiesWithAIContext: 0,
        entitiesWithCodeMapping: 0
      },
      quality: {
        averageDocLength: 0,
        entitiesWithExamples: 0,
        brokenReferences: 0,
        outdatedReferences: 0
      },
      compliance: {
        requiredFieldsComplete: 0,
        validationPassing: 0,
        styleGuideCompliant: 0
      }
    };
  }

  collectMetrics() {
    console.log('📊 Collecting documentation metrics...');
    
    const entityDirs = this.findEntityDirs('./entities');
    this.metrics.coverage.totalEntities = entityDirs.length;
    
    entityDirs.forEach(dir => {
      this.analyzeEntity(dir);
    });
    
    this.calculateAverages();
    return this.metrics;
  }

  analyzeEntity(entityDir) {
    const indexPath = path.join(entityDir, 'index.md');
    const catalogPath = path.join(entityDir, 'catalog-info.yaml');
    const soulPath = path.join(entityDir, 'soul.yaml');
    
    // Check documentation presence
    if (fs.existsSync(indexPath)) {
      this.metrics.coverage.entitiesWithDocs++;
      
      const content = fs.readFileSync(indexPath, 'utf8');
      
      // Check AI Context Header
      if (content.includes('## AI Context Header')) {
        this.metrics.coverage.entitiesWithAIContext++;
      }
      
      // Check Code Mapping
      if (content.includes('## Code Mapping')) {
        this.metrics.coverage.entitiesWithCodeMapping++;
      }
      
      // Check for examples
      if (content.includes('```') || content.includes('Example:')) {
        this.metrics.quality.entitiesWithExamples++;
      }
      
      // Analyze quality metrics
      this.analyzeDocumentQuality(content, entityDir);
    }
    
    // Check compliance
    this.analyzeCompliance(entityDir);
  }

  analyzeDocumentQuality(content, entityDir) {
    // Check for broken internal references
    const internalLinks = content.match(/\[.*?\]\((?!http).*?\)/g) || [];
    internalLinks.forEach(link => {
      const linkPath = link.match(/\((.*?)\)/)[1];
      const fullPath = path.resolve(entityDir, linkPath);
      if (!fs.existsSync(fullPath)) {
        this.metrics.quality.brokenReferences++;
      }
    });
    
    // Check for code references that might be outdated
    const codeRefs = content.match(/`src\/[^`]+`/g) || [];
    codeRefs.forEach(ref => {
      const filePath = ref.replace(/`/g, '').split(':')[0];
      if (!fs.existsSync(filePath)) {
        this.metrics.quality.outdatedReferences++;
      }
    });
  }

  analyzeCompliance(entityDir) {
    const catalogPath = path.join(entityDir, 'catalog-info.yaml');
    const soulPath = path.join(entityDir, 'soul.yaml');
    
    let compliant = true;
    
    // Check required files
    if (!fs.existsSync(catalogPath) || !fs.existsSync(soulPath)) {
      compliant = false;
    }
    
    // Check required fields
    if (fs.existsSync(soulPath)) {
      try {
        const soul = yaml.load(fs.readFileSync(soulPath, 'utf8'));
        if (!soul?.entityMetadata?.uuid || !soul?.entityMetadata?.name) {
          compliant = false;
        }
      } catch (error) {
        compliant = false;
      }
    }
    
    if (compliant) {
      this.metrics.compliance.requiredFieldsComplete++;
    }
  }

  calculateAverages() {
    const total = this.metrics.coverage.totalEntities;
    if (total > 0) {
      this.metrics.coverage.coveragePercentage = 
        Math.round((this.metrics.coverage.entitiesWithDocs / total) * 100);
      this.metrics.coverage.aiContextPercentage = 
        Math.round((this.metrics.coverage.entitiesWithAIContext / total) * 100);
      this.metrics.coverage.codeMappingPercentage = 
        Math.round((this.metrics.coverage.entitiesWithCodeMapping / total) * 100);
      this.metrics.compliance.compliancePercentage = 
        Math.round((this.metrics.compliance.requiredFieldsComplete / total) * 100);
    }
  }

  findEntityDirs(basePath) {
    const dirs = [];
    
    function traverse(currentPath) {
      if (!fs.existsSync(currentPath)) return;
      
      const items = fs.readdirSync(currentPath, { withFileTypes: true });
      
      items.forEach(item => {
        if (item.isDirectory()) {
          const fullPath = path.join(currentPath, item.name);
          
          if (fs.existsSync(path.join(fullPath, 'catalog-info.yaml'))) {
            dirs.push(fullPath);
          } else {
            traverse(fullPath);
          }
        }
      });
    }
    
    traverse(basePath);
    return dirs;
  }

  generateReport() {
    const metrics = this.collectMetrics();
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalEntities: metrics.coverage.totalEntities,
        documentationCoverage: `${metrics.coverage.coveragePercentage}%`,
        complianceRate: `${metrics.compliance.compliancePercentage}%`,
        qualityScore: this.calculateQualityScore(metrics)
      },
      details: metrics
    };
    
    // Write JSON report
    fs.writeFileSync('documentation-metrics.json', JSON.stringify(report, null, 2));
    
    // Generate HTML dashboard
    this.generateHTMLDashboard(report);
    
    console.log('📊 Metrics Report Generated:');
    console.log(`  Total Entities: ${report.summary.totalEntities}`);
    console.log(`  Documentation Coverage: ${report.summary.documentationCoverage}`);
    console.log(`  Compliance Rate: ${report.summary.complianceRate}`);
    console.log(`  Quality Score: ${report.summary.qualityScore}/100`);
    
    return report;
  }

  calculateQualityScore(metrics) {
    const weights = {
      coverage: 0.3,
      aiContext: 0.2,
      codeMapping: 0.2,
      examples: 0.1,
      compliance: 0.2
    };
    
    const scores = {
      coverage: metrics.coverage.coveragePercentage || 0,
      aiContext: metrics.coverage.aiContextPercentage || 0,
      codeMapping: metrics.coverage.codeMappingPercentage || 0,
      examples: metrics.coverage.totalEntities > 0 ? 
        Math.round((metrics.quality.entitiesWithExamples / metrics.coverage.totalEntities) * 100) : 0,
      compliance: metrics.compliance.compliancePercentage || 0
    };
    
    const weightedScore = Object.keys(weights).reduce((total, key) => {
      return total + (scores[key] * weights[key]);
    }, 0);
    
    return Math.round(weightedScore);
  }

  generateHTMLDashboard(report) {
    const html = `
<!DOCTYPE html>
<html>
<head>
    <title>Documentation Health Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .metric-card { 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            padding: 20px; 
            margin: 10px; 
            display: inline-block; 
            min-width: 200px;
        }
        .metric-value { font-size: 2em; font-weight: bold; color: #2196F3; }
        .metric-label { color: #666; margin-top: 5px; }
        .quality-score { color: ${report.summary.qualityScore >= 80 ? '#4CAF50' : report.summary.qualityScore >= 60 ? '#FF9800' : '#F44336'}; }
    </style>
</head>
<body>
    <h1>📊 Documentation Health Dashboard</h1>
    <p>Generated: ${report.timestamp}</p>
    
    <div class="metric-card">
        <div class="metric-value">${report.summary.totalEntities}</div>
        <div class="metric-label">Total Entities</div>
    </div>
    
    <div class="metric-card">
        <div class="metric-value">${report.summary.documentationCoverage}</div>
        <div class="metric-label">Documentation Coverage</div>
    </div>
    
    <div class="metric-card">
        <div class="metric-value">${report.summary.complianceRate}</div>
        <div class="metric-label">Compliance Rate</div>
    </div>
    
    <div class="metric-card">
        <div class="metric-value quality-score">${report.summary.qualityScore}/100</div>
        <div class="metric-label">Quality Score</div>
    </div>
    
    <h2>Detailed Metrics</h2>
    <pre>${JSON.stringify(report.details, null, 2)}</pre>
</body>
</html>
    `;
    
    fs.writeFileSync('documentation-dashboard.html', html);
  }
}

// Generate report
const metrics = new DocumentationMetrics();
metrics.generateReport();
```

## Troubleshooting Guide

### Common Validation Errors

#### 1. Missing Reference Errors

**Error**: `References non-existent API: api:user-service`

**Cause**: Entity references another entity that doesn't exist or is incorrectly named

**Solutions**:
```bash
# Check if the referenced entity exists
find entities -name "catalog-info.yaml" -exec grep -l "name: user-service" {} \;

# Check for typos in the reference
grep -r "user-service" entities/

# Verify the entity kind is correct
grep -A 5 -B 5 "user-service" entities/*/catalog-info.yaml
```

#### 2. Code Mapping Reference Errors

**Error**: `Referenced file does not exist: src/services/auth.ts`

**Cause**: Documentation references code files that have been moved or deleted

**Solutions**:
```bash
# Find where the file might have moved
find . -name "auth.ts" -type f

# Check git history for file moves
git log --follow --name-status -- src/services/auth.ts

# Update documentation with correct path
sed -i 's|src/services/auth.ts|src/auth/auth.service.ts|g' docs/components/*/index.md
```

#### 3. YAML Syntax Errors

**Error**: `YAML syntax error in entities/services/auth-service/catalog-info.yaml`

**Cause**: Invalid YAML formatting

**Solutions**:
```bash
# Check YAML syntax
yamllint entities/services/auth-service/catalog-info.yaml

# Common issues:
# - Incorrect indentation
# - Missing quotes around special characters
# - Invalid list formatting

# Fix indentation
sed -i 's/^  /    /g' catalog-info.yaml  # Convert 2-space to 4-space indentation
```

#### 4. Missing Required Fields

**Error**: `soul.yaml missing entityMetadata.uuid`

**Cause**: Required fields not present in soul.yaml

**Solutions**:
```bash
# Generate UUID for missing entities
uuidgen | tr '[:upper:]' '[:lower:]'

# Add missing UUID to soul.yaml
cat >> entities/services/auth-service/soul.yaml << EOF
entityMetadata:
  uuid: $(uuidgen | tr '[:upper:]' '[:lower:]')
  name: auth-service
EOF
```

### Validation Bypass (Emergency Only)

**When to use**: Critical production issues requiring immediate deployment

**How to bypass**:
```bash
# Skip pre-commit hooks (emergency only)
git commit --no-verify -m "Emergency fix: bypass validation"

# Skip CI validation (requires admin privileges)
git push -o ci.skip
```

**Post-bypass requirements**:
1. Create immediate follow-up ticket to fix validation issues
2. Document bypass reason and remediation plan
3. Fix validation issues within 24 hours
4. Review bypass usage in retrospective

### Debugging Validation Scripts

**Enable debug mode**:
```bash
# Enable verbose output
export DEBUG=1
./scripts/validate-semantic.js

# Run individual validation steps
./scripts/validate-yaml-syntax.sh --verbose
./scripts/check-api-docs.js --debug
```

**Common debugging techniques**:
```bash
# Check script permissions
ls -la scripts/
chmod +x scripts/*.sh

# Verify dependencies
which yamllint jq node npm

# Test with single entity
./scripts/validate-semantic.js --entity entities/services/auth-service

# Generate detailed logs
./scripts/validate-semantic.js 2>&1 | tee validation.log
```

## Configuration

### Environment Variables

```bash
# Validation configuration
export CORTEX_VALIDATION_LEVEL=strict    # strict|normal|permissive
export CORTEX_ENTITIES_PATH=./entities   # Path to entities directory
export CORTEX_DOCS_PATH=./docs           # Path to documentation
export CORTEX_SKIP_WARNINGS=false        # Skip warning-level issues
export CORTEX_PARALLEL_VALIDATION=true   # Enable parallel processing
```

### Validation Configuration File

**Create**: `.cortex-validation.yaml`
```yaml
validation:
  level: strict  # strict|normal|permissive
  
  paths:
    entities: ./entities
    docs: ./docs
    source: ./src
    
  rules:
    syntax:
      enabled: true
      fail_on_error: true
      
    semantic:
      enabled: true
      fail_on_error: true
      fail_on_warnings: false
      
    business:
      enabled: true
      require_manual_review: true
      
  integrations:
    pre_commit: true
    github_actions: true
    gitlab_ci: true
    
  reporting:
    generate_html: true
    generate_json: true
    metrics_collection: true
    
  notifications:
    slack_webhook: ${SLACK_WEBHOOK_URL}
    email_recipients: []
```

This comprehensive validation framework ensures that the Cortex methodology maintains high quality, consistency, and reliability throughout the development lifecycle while providing clear feedback and automated enforcement of standards.